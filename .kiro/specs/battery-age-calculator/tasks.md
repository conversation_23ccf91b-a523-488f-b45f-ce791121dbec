# Implementation Plan

- [x] 1. Set up project structure and core interfaces

  - Create battery_age_calculator.py with main BatteryAgeCalculator class
  - Define data models and interfaces for battery timeline and age calculation
  - Set up logging configuration and error handling framework
  - _Requirements: 1.1, 4.1_

- [x] 2. Implement data loading components

  - [x] 2.1 Create TimelineLoader class for CSV data loading

    - Implement CSV loading with pandas and proper data type handling
    - Add validation for required columns (battery_id, vin, start_date, end_date, is_currently_active)
    - Handle date parsing with error handling for malformed dates
    - _Requirements: 1.1, 3.4_

  - [x] 2.2 Implement DatabaseConnector for PostgreSQL integration

    - Create database connection using SQLAlchemy with connection string from environment
    - Implement VIN to vehicle_id mapping query from vehicles table
    - Add connection error handling and graceful fallbacks
    - _Requirements: 2.5_

  - [x] 2.3 Create daily stats query functionality
    - Load daily stats data from CSV file and pre-index by vehicle_id for fast lookup
    - Implement method to get latest daily stats date for a given vehicle_id using CSV data
    - Add batch querying capability for multiple vehicles using pre-indexed CSV data
    - Handle cases where no daily stats exist for a vehicle
    - _Requirements: 2.2, 2.3, 2.4_

- [x] 3. Implement battery data processing logic

  - [x] 3.1 Create BatteryGrouper class for timeline aggregation

    - Group timeline records by battery_id into collections
    - Find earliest start_date across all periods for each battery
    - Find latest end_date across all periods for each battery
    - _Requirements: 1.2, 1.3_

  - [x] 3.2 Implement missing date handling logic

    - Handle missing start dates by using next available start_date for same battery
    - Handle missing end dates by using previous available end_date for same battery
    - Add validation to ensure date logic is sound
    - _Requirements: 1.4, 1.5_

  - [x] 3.3 Create ActiveBatteryProcessor for end date filling
    - Identify batteries with is_currently_active = True
    - Query latest daily stats for each active battery's VIN using vehicle_id mapping
    - Update end_date with latest activity date or current date as fallback
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 4. Implement age calculation engine

  - [x] 4.1 Create AgeCalculator class with core calculation logic

    - Calculate age in days using (latest_end_date - earliest_start_date).days
    - Round results to 2 decimal places for readability
    - Handle edge cases like same-day periods
    - _Requirements: 3.1, 3.2_

  - [x] 4.2 Add comprehensive error handling and validation
    - Validate that end_date is after start_date
    - Handle pandas NaT values and None dates gracefully
    - Generate descriptive error messages for note column
    - _Requirements: 3.4, 3.5_

- [x] 5. Implement CSV export functionality

  - [x] 5.1 Create ResultExporter class for CSV output

    - Generate CSV with columns: battery_id, age_days, note
    - Handle successful calculations and error cases appropriately
    - Use descriptive filename with timestamp
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

  - [x] 5.2 Add processing statistics and summary reporting
    - Count total batteries processed, successful calculations, and errors
    - Log summary statistics during processing
    - Include data quality metrics in console output
    - _Requirements: 4.1_

- [x] 6. Create main execution script and CLI interface

  - [x] 6.1 Implement main() function with command-line argument parsing

    - Accept timeline CSV path, daily stats CSV path, and output path as arguments
    - Add database connection string configuration via environment variables
    - Provide usage help and parameter validation
    - _Requirements: 1.1, 2.5, 4.5_

  - [x] 6.2 Add comprehensive error handling and user feedback
    - Handle file not found errors gracefully
    - Provide clear error messages for database connection issues
    - Show progress indicators for long-running operations
    - _Requirements: 3.4, 2.4_

- [x] 7. Create unit tests for core functionality

  - [x] 7.1 Write tests for BatteryGrouper and date handling logic

    - Test battery period grouping with various scenarios
    - Test missing date handling with different combinations
    - Test edge cases like single periods and multiple transfers
    - _Requirements: 1.2, 1.3, 1.4, 1.5_

  - [x] 7.2 Write tests for AgeCalculator and validation logic

    - Test age calculation with normal date ranges
    - Test error handling for invalid date ranges (end before start)
    - Test leap year handling and decimal precision
    - _Requirements: 3.1, 3.2, 3.4, 3.5_

  - [x] 7.3 Write integration tests for database connectivity
    - Test VIN to vehicle_id mapping functionality
    - Test daily stats querying with mock data
    - Test graceful handling of database connection failures
    - _Requirements: 2.2, 2.3, 2.4, 2.5_

- [x] 8. Create example usage script and documentation

  - [x] 8.1 Write example script demonstrating typical usage

    - Show how to run the calculator with sample data
    - Demonstrate error handling and output interpretation
    - Include performance considerations for large datasets
    - _Requirements: 4.1, 4.5_

  - [x] 8.2 Add comprehensive docstrings and code documentation
    - Document all classes and methods with clear descriptions
    - Add parameter and return value documentation
    - Include usage examples in docstrings
    - _Requirements: 1.1_
