# Design Document

## Overview

The Battery Age Calculator is a Python script that processes battery timeline data to calculate the total age of each battery from their earliest start date to their latest end date. The system handles missing end dates for currently active batteries by querying the latest daily stats data, and exports results to a CSV file with proper error handling.

## Architecture

The system follows a pipeline architecture with these main components:

1. **Data Loading Layer**: Loads battery timeline CSV and establishes database connection for VIN mapping
2. **Data Processing Layer**: Groups battery periods, fills missing dates, and calculates ages
3. **Database Integration Layer**: Queries daily stats for latest activity dates
4. **Export Layer**: Generates CSV output with results and error notes 

## Components and Interfaces

### BatteryAgeCalculator Class

Main orchestrator class that coordinates the age calculation process.

**Key Methods:**

- `__init__(timeline_csv_path, daily_stats_csv_path, db_connection_string)`: Initialize with file paths and database connection
- `calculate_battery_ages()`: Main method that orchestrates the entire calculation process
- `export_results(output_path)`: Export results to CSV file

### Data Loading Components

**TimelineLoader**

- Loads battery timeline CSV using pandas
- Validates required columns: battery_id, vin, start_date, end_date, is_currently_active
- Handles date parsing and data type conversion

**DatabaseConnector**

- Establishes PostgreSQL connection using SQLAlchemy (for VIN mapping only)
- Loads VIN to vehicle_id mapping from vehicles table
- Loads daily stats data from CSV file and pre-indexes by vehicle_id for fast lookup
- Provides CSV-based daily stats queries for latest activity dates

### Data Processing Components

**BatteryGrouper**

- Groups timeline records by battery_id
- Identifies earliest start_date and latest end_date for each battery
- Handles missing dates using fallback logic

**ActiveBatteryProcessor**

- Identifies batteries with is_currently_active = True
- Queries latest daily stats for each active battery's VIN
- Updates end_date with latest activity date

**AgeCalculator**

- Calculates age in days using (end_date - start_date).days
- Handles edge cases and validation
- Generates error notes for problematic calculations

## Data Models

### BatteryTimeline (Input)

```python
{
    'battery_id': str,
    'vin': str,
    'start_date': datetime,
    'end_date': datetime,
    'is_currently_active': bool
}
```

### BatteryAge (Output)

```python
{
    'battery_id': str,
    'earliest_start': datetime,
    'latest_end': datetime,
    'age_days': float,
    'note': str
}
```

### VINMapping

```python
{
    'vin': str,
    'vehicle_id': int
}
```

## Error Handling

### Missing Data Handling

- **Missing start dates**: Use next available start_date for the same battery
- **Missing end dates**: Use previous available end_date for the same battery
- **No dates available**: Record error note and skip age calculation

### Database Connection Issues

- Graceful fallback to current date if daily stats query fails
- Log warnings for connection issues but continue processing
- Validate VIN mapping availability before querying

### Date Validation

- Check for end_date before start_date scenarios
- Handle pandas NaT (Not-a-Time) values
- Validate date arithmetic operations

### Data Quality Issues

- Handle duplicate battery_id entries
- Validate required columns exist in input CSV
- Check for malformed date strings

## Testing Strategy

### Unit Tests

- Test BatteryGrouper with various battery period combinations
- Test AgeCalculator with edge cases (same day periods, leap years)
- Test ActiveBatteryProcessor with missing VIN mappings
- Test date handling with NaT values and invalid dates

### Integration Tests

- Test full pipeline with sample battery timeline data
- Test database connectivity and VIN mapping queries
- Test CSV export functionality with various result scenarios

### Data Validation Tests

- Test with batteries having single vs multiple periods
- Test with all active vs all inactive batteries
- Test with missing start/end dates in various combinations
- Test with invalid date ranges (end before start)

### Performance Tests

- Test with large battery timeline datasets (10k+ records)
- Measure memory usage during data processing
- Validate database query performance for VIN lookups

## Implementation Notes

### Database Query Optimization

- Use batch queries for VIN to vehicle_id mapping
- Index daily_stats queries by vehicle_id and date
- Implement connection pooling for multiple queries

### Memory Management

- Process batteries in batches if dataset is very large
- Use pandas chunking for CSV reading if needed
- Clear intermediate dataframes after processing

### Date Handling Best Practices

- Use pandas datetime parsing with error handling
- Convert all dates to consistent timezone (UTC)
- Handle leap year calculations properly (365.25 days/year)

### Error Reporting

- Provide detailed error messages in note column
- Log processing statistics (total batteries, successful calculations, errors)
- Include data quality metrics in output summary
