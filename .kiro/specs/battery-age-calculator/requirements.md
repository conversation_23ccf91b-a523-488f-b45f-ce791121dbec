# Requirements Document

## Introduction

This feature calculates the age of each battery based on their complete timeline data from the battery timeline CSV. The system needs to determine the earliest start date and latest end date for each battery across all their periods, fill in missing end dates for currently active batteries using the latest daily stats data, and calculate the total age in years.

## Requirements

### Requirement 1

**User Story:** As a fleet analyst, I want to calculate the age of each battery from their complete timeline, so that I can analyze battery lifecycle and performance patterns.

#### Acceptance Criteria

1. WHEN processing battery timeline data THEN the system SHALL identify all periods for each unique battery_id
2. WHEN multiple periods exist for a battery THEN the system SHALL find the earliest start_date across all periods
3. WHEN multiple periods exist for a battery THEN the system SHALL find the latest end_date across all periods
4. WHEN a battery has missing start dates THEN the system SHALL use the next available start_date for that battery
5. WHEN a battery has missing end dates THEN the system SHALL use the previous available end_date for that battery

### Requirement 2

**User Story:** As a fleet analyst, I want missing end dates filled for currently active batteries, so that I can calculate accurate battery ages including ongoing usage.

#### Acceptance Criteria

1. WHEN a battery has is_currently_active = True THEN the system SHALL identify the VIN for that active period
2. WHEN filling end dates for active batteries THEN the system SHALL load daily stats data from CSV file
3. WHEN daily stats data exists for the VIN THEN the system SHALL use the latest date as the end_date
4. WHEN no daily stats data exists for the VIN THEN the system SHALL record a detailed error note explaining the mapping failure
5. WHEN accessing daily stats THEN the system SHALL use the VIN to vehicle_id mapping from the database and pre-indexed CSV data for fast lookup
6. WHEN VIN to vehicle_id mapping fails THEN the system SHALL record the specific VIN that failed mapping in the error note
7. WHEN database connection fails THEN the system SHALL record database connectivity issues in the error note

### Requirement 3

**User Story:** As a fleet analyst, I want battery ages calculated in years with proper error handling, so that I can get reliable age metrics for analysis.

#### Acceptance Criteria

1. WHEN calculating age THEN the system SHALL subtract earliest_start_date from latest_end_date
2. Age of batteries shall be display in days
3. WHEN calculation succeeds THEN the system SHALL round the age to 2 decimal places
4. WHEN start_date or end_date is missing THEN the system SHALL record an error note and skip age calculation
5. WHEN end_date is before start_date THEN the system SHALL record an error note about invalid date range

### Requirement 4

**User Story:** As a fleet analyst, I want alternative data sources used for end date determination when daily stats mapping fails, so that I can get complete battery age calculations even when primary data sources have gaps.

#### Acceptance Criteria

1. WHEN VIN to vehicle_id mapping fails for daily stats THEN the system SHALL attempt to use repair history data as alternative source
2. WHEN repair history contains battery replacement events THEN the system SHALL use the replacement date as the end_date
3. WHEN multiple repair events exist for the same battery THEN the system SHALL use the latest replacement date
4. WHEN repair history shows battery installation followed by replacement THEN the system SHALL validate the date sequence
5. WHEN alternative data sources are used THEN the system SHALL record the data source in the note column

### Requirement 5

**User Story:** As a fleet analyst, I want the results exported to a CSV file with clear columns, so that I can easily analyze and share battery age data.

#### Acceptance Criteria

1. WHEN exporting results THEN the system SHALL create a CSV with columns: battery_id, age (in days), note
2. WHEN age calculation succeeds THEN the system SHALL populate age in days with the calculated value
3. WHEN errors occur THEN the system SHALL populate the note column with error details
4. WHEN no errors occur THEN the system SHALL leave the note column empty
5. WHEN saving the file THEN the system SHALL use a descriptive filename with timestamp
