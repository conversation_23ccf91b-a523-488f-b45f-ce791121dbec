# Project Structure

## Root Directory Organization

```
├── config.py                          # Database and file path configuration
├── requirements.txt                   # Python dependencies
├── tables.sql                        # Database schema definitions
├── README.md                         # Project documentation
└── *.csv, *.xlsx                     # Data files (repair history, mappings)
```

## Core Analysis Scripts

```
├── battery_km_calculator.py          # Main analysis script (original approach)
├── data_preparation_pipeline.py      # Enhanced 4-phase pipeline analysis
├── vehicle_battery_comparison.py     # Vehicle-battery relationship analysis
└── export_daily_stats.py            # Daily statistics export utility
```

## Testing and Validation Scripts

```
├── test_connection.py                # Database connectivity testing
├── test_database_connectivity.py     # Extended database tests
├── test_*.py                         # Specific feature and edge case tests
└── count_csv_records.py             # CSV file record counting utility
```

## Utility and Generation Scripts

```
├── generate_working_csvs.py          # Working dataset generation
└── export_daily_stats.py            # Statistics export functionality
```

## Output Directories

```
├── battery_timeline_results/         # Main pipeline analysis results
├── test_battery_results/            # Test run outputs
└── comparison_results/              # Vehicle-battery comparison outputs
```

## Code Organization Patterns

### Script Structure
- All main scripts use `#!/usr/bin/env python3` shebang
- Comprehensive docstrings with multi-line descriptions
- Class-based architecture for main analysis components
- Logging configured at module level with INFO level default

### Database Interaction
- SQLAlchemy for ORM and connection management
- Connection strings built from `config.py` or environment variables
- Graceful error handling for database connectivity issues

### Data Processing
- pandas for all data manipulation and analysis
- CSV files processed with semicolon separators (`;`)
- Date handling with pandas datetime conversion and error handling
- Comprehensive data validation and cleaning pipelines

### File Naming Conventions
- Snake_case for all Python files
- Descriptive names indicating functionality (`test_`, `export_`, `generate_`)
- Results stored in dedicated subdirectories with descriptive names