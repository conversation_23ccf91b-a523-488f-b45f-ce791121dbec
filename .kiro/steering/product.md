# Product Overview

## Battery Kilometers Calculator

A comprehensive data analysis system that tracks battery lifecycle and usage across electric vehicle fleets. The system calculates total kilometers traveled by each battery as they move between vehicles, providing insights into battery performance, utilization, and fleet optimization.

## Core Functionality

- **Battery Timeline Creation**: Builds chronological timelines of battery installations and removals across vehicles using a sophisticated 4-phase hybrid approach
- **Kilometer Calculation**: Aggregates total distance traveled by each battery across all vehicles using odometer data
- **Cross-Vehicle Tracking**: Follows batteries as they transfer between different vehicles with proper conflict resolution
- **Data Quality Validation**: Identifies and handles data anomalies, missing records, and edge cases with comprehensive validation rules
- **Fleet Analytics**: Provides summary statistics and insights for fleet management decisions

## Key Use Cases

- Battery lifecycle analysis and performance tracking across vehicle swaps
- Fleet optimization and battery allocation decisions
- Maintenance scheduling based on battery usage patterns and total kilometers
- Data quality assessment for vehicle telemetry systems
- Historical analysis of battery swapping operations and cross-vehicle transfers
- Vehicle activity validation to ensure timeline completeness

## Data Sources

- **PostgreSQL Database**: Vehicle telemetry, daily statistics, and VIN mappings
- **CSV Files**: Battery repair/change history, vehicle snapshots, and activity data
- **Excel Files**: Vehicle-battery mapping data and configuration files

## Architecture Approach

The system uses a hybrid CSV + PostgreSQL approach for optimal performance:
- CSV files for bulk data processing and performance-critical operations
- PostgreSQL for accurate VIN mappings and real-time data validation
- Multi-phase processing pipeline with comprehensive conflict resolution