# Technology Stack

## Core Technologies

- **Python 3.8+**: Primary programming language
- **PostgreSQL**: Database for vehicle telemetry and daily statistics
- **pandas**: Data manipulation and analysis
- **SQLAlchemy**: Database ORM and connection management
- **psycopg2-binary**: PostgreSQL adapter for Python
- **numpy**: Numerical computing
- **openpyxl**: Excel file processing

## Development Environment

- **Virtual Environment**: Use `venv` for dependency isolation
- **Requirements**: All dependencies listed in `requirements.txt`

## Database Configuration

- Connection settings managed via `config.py` and environment variables
- Support for both direct connection and Docker-mapped ports
- Database schema defined in `tables.sql`

## Common Commands

### Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### Database Connection Testing
```bash
# Test basic database connectivity
python test_connection.py
python test_database_connectivity.py
```

### Main Analysis Execution
```bash
# CSV-only analysis (no database required)
python test_csv_data.py

# Full analysis with database
python battery_km_calculator.py

# Enhanced pipeline analysis
python data_preparation_pipeline.py
```

### Utility Scripts
```bash
# Count CSV records
python count_csv_records.py

# Generate working CSV files
python generate_working_csvs.py

# Export daily statistics
python export_daily_stats.py
```

## Configuration Management

- Database credentials via environment variables or `config.py`
- File paths configurable in `config.py`
- Logging configured at module level with INFO level default