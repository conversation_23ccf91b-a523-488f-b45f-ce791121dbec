#!/usr/bin/env python3
"""
Test script for data loading components of Battery Age Calculator.
"""

import logging
import sys
from battery_age_calculator import TimelineLoader, DatabaseConnector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_timeline_loader():
    """Test the TimelineLoader class."""
    logger.info("=== Testing TimelineLoader ===")
    
    try:
        # Test with the sample CSV file
        loader = TimelineLoader("days_calculate/battery_timeline.csv")
        data = loader.load_timeline_data()
        
        logger.info(f"Successfully loaded {len(data)} records")
        logger.info(f"Columns: {list(data.columns)}")
        logger.info(f"Sample data:")
        print(data.head())
        
        return True
        
    except Exception as e:
        logger.error(f"TimelineLoader test failed: {str(e)}")
        return False

def test_database_connector():
    """Test the DatabaseConnector class."""
    logger.info("=== Testing DatabaseConnector ===")
    
    try:
        # Test connection
        db_connector = DatabaseConnector()
        
        # Test connection without actually connecting (to avoid DB dependency)
        logger.info("DatabaseConnector initialized successfully")
        
        # Test VIN mapping method (will fail gracefully without DB)
        test_vins = ["WS5D16GAAJA100879", "WS5D16JAAKA800003"]
        logger.info(f"Testing VIN mapping interface with {len(test_vins)} VINs")
        
        # This will fail gracefully without actual DB connection
        try:
            mappings = db_connector.load_vin_mappings(test_vins)
            logger.info(f"VIN mappings: {mappings}")
        except Exception as e:
            logger.info(f"VIN mapping failed as expected without DB: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"DatabaseConnector test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting data loading component tests")
    
    timeline_test = test_timeline_loader()
    db_test = test_database_connector()
    
    if timeline_test and db_test:
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())