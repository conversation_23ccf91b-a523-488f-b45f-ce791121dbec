"""
Configuration file for Battery Kilometers Calculator
"""

import os

# Database Configuration
# Update these values with your actual database credentials
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '6543'),  # <PERSON><PERSON> mapped port
    'database': os.getenv('DB_NAME', 'LeitwartenDB'),
    'username': os.getenv('DB_USER', 'datadump'),
    'password': os.getenv('DB_PASSWORD', 'pAUjuLftyHURa5Ra'),
}


# Build connection string
DB_CONNECTION_STRING = (
    f"postgresql://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}"
    f"@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
)

# File paths
CSV_FILE_PATH = "hv_repair_2025-06-02b.csv"
OUTPUT_DIR = "output"