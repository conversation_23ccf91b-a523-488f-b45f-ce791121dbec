#!/usr/bin/env python3
"""
Test script for CSV-based daily stats functionality.
"""

import logging
import sys
from battery_age_calculator import DatabaseConnector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_csv_daily_stats():
    """Test the CSV-based daily stats functionality."""
    logger.info("=== Testing CSV-based Daily Stats Functionality ===")
    
    try:
        # Initialize DatabaseConnector with CSV path
        db_connector = DatabaseConnector(daily_stats_csv_path="daily_stats.csv")
        
        # Load daily stats from CSV
        db_connector.load_daily_stats_csv()
        logger.info(f"✓ CSV Loading: Loaded daily stats for {len(db_connector.daily_stats_by_vehicle)} vehicles")
        
        # Test database connection for VIN mapping
        if db_connector.connect():
            logger.info("✓ Database Connection: Successfully connected for VIN mapping")
            
            # Test VIN mapping
            test_vins = ["WS5D16GAAJA100879", "WS5D16JAAKA800003"]
            mappings = db_connector.load_vin_mappings(test_vins)
            logger.info(f"✓ VIN Mappings: {len(mappings)} VINs mapped to vehicle_ids")
            
            if mappings:
                # Test single vehicle daily stats query
                first_vin = list(mappings.keys())[0]
                first_vehicle_id = mappings[first_vin]
                latest_date = db_connector.get_latest_daily_stats_date(first_vehicle_id)
                logger.info(f"✓ Single Query: Latest date for vehicle_id {first_vehicle_id}: {latest_date}")
                
                # Test batch vehicle daily stats query
                vehicle_ids = list(mappings.values())
                batch_dates = db_connector.get_latest_daily_stats_dates_batch(vehicle_ids)
                logger.info(f"✓ Batch Query: Retrieved dates for {len(batch_dates)} vehicles")
                
                # Test VIN-based daily stats query
                vin_date = db_connector.get_latest_daily_stats_for_vin(first_vin)
                logger.info(f"✓ VIN Query: Latest date for VIN {first_vin}: {vin_date}")
                
                # Test batch VIN daily stats query
                batch_vin_dates = db_connector.get_latest_daily_stats_for_vins_batch(test_vins)
                logger.info(f"✓ Batch VIN Query: Retrieved dates for {len(batch_vin_dates)} VINs")
                
        else:
            logger.warning("✗ Database Connection: Failed to connect for VIN mapping")
            
        return True
        
    except Exception as e:
        logger.error(f"✗ CSV daily stats test failed: {str(e)}")
        return False

def main():
    """Run CSV daily stats tests."""
    logger.info("Starting CSV-based daily stats tests")
    
    if test_csv_daily_stats():
        logger.info("=== All CSV daily stats tests passed! ===")
        return 0
    else:
        logger.error("=== CSV daily stats tests failed! ===")
        return 1

if __name__ == "__main__":
    sys.exit(main())