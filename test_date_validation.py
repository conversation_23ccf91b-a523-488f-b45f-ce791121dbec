#!/usr/bin/env python3
"""
Test script to verify that the enhanced battery transfer logic
properly validates that found events are earlier than the before_date. used for testing the date validation logic in the data preparation pipeline.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class MockPipelineForDateTest:
    """Mock pipeline to test date validation."""

    def __init__(self):
        # Test data with events AFTER the before_date (should be ignored)
        self.cleaned_events = [
            # This event is AFTER 2022-01-28 (should be ignored)
            {
                "date": datetime(2022, 2, 15),  # AFTER before_date
                "vin": "WS5D16BDAHA100693",
                "battery_id_old": "10459",
                "battery_id_new": None,
                "action": "install",
            },
            # This event is BEFORE 2022-01-28 (should be found)
            {
                "date": datetime(2021, 12, 15),  # BEFORE before_date
                "vin": "WS5D16BDAHA100693",
                "battery_id_old": "10459",
                "battery_id_new": None,
                "action": "install",
            },
            # This event is AFTER 2022-01-28 (should be ignored)
            {
                "date": datetime(2022, 3, 1),  # AFTER before_date
                "vin": "WS5D16BDAHA100693",
                "battery_id_new": "10459",
                "battery_id_old": None,
                "action": "install",
            },
        ]

        self.vehicle_info_cache = {
            "WS5D16BDAHA100693": {
                "vin": "WS5D16BDAHA100693",
                "erstzulassung": datetime(2021, 1, 15),  # BEFORE before_date
                "master_battery": "10459",
                "slave_battery": None,
            }
        }

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Same logic as the enhanced method."""
        vehicle_info = self.vehicle_info_cache.get(vin, {})

        print(f"\n=== Testing date validation for battery {battery_id} ===")
        print(f"Before date constraint: {before_date}")
        print(f"Available events:")
        for i, event in enumerate(self.cleaned_events):
            print(
                f"  Event {i+1}: {event['date']} - Valid? {not before_date or event['date'] < before_date}"
            )

        # Priority 1: Standard installations
        install_events = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and (not before_date or e["date"] < before_date)  # DATE VALIDATION
        ]

        print(
            f"\nPriority 1 - Standard installations: {len(install_events)} valid events"
        )
        if install_events:
            earliest = min(install_events, key=lambda x: x["date"])
            print(f"  → Found: {earliest['date']} (should be before {before_date})")
            return earliest["date"], "repair_event_found", "high"

        # Priority 2: Implied installations
        implied_install_events = [
            e
            for e in self.cleaned_events
            if e["battery_id_old"] == battery_id
            and e["battery_id_new"] is None
            and (not before_date or e["date"] < before_date)  # DATE VALIDATION
        ]

        print(
            f"Priority 2 - Implied installations: {len(implied_install_events)} valid events"
        )
        if implied_install_events:
            earliest = min(implied_install_events, key=lambda x: x["date"])
            print(f"  → Found: {earliest['date']} (should be before {before_date})")
            return earliest["date"], "implied_installation_found", "medium"

        # Priority 5: Snapshot matching (skipping 3,4 for simplicity)
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        print(
            f"Priority 5 - Snapshot matching: Battery in snapshot? {battery_id in snapshot_batteries}"
        )
        if battery_id in snapshot_batteries:
            erstzulassung = vehicle_info.get("erstzulassung")
            if erstzulassung and (
                not before_date or erstzulassung < before_date
            ):  # DATE VALIDATION
                print(f"  → Found: {erstzulassung} (should be before {before_date})")
                return erstzulassung, "snapshot_match_erstzulassung", "high"

        return None, "no_start_found", "low"


def test_date_validation():
    """Test that date validation works correctly."""

    print("TESTING DATE VALIDATION IN ENHANCED LOGIC")
    print("=" * 60)
    print("Scenario: Events exist both BEFORE and AFTER the constraint date")
    print("Expected: Only events BEFORE the constraint should be found")
    print()

    pipeline = MockPipelineForDateTest()

    # Test case: Find battery start before 2022-01-28
    battery_id = "10459"
    before_date = datetime(2022, 1, 28)
    vin = "WS5D16BDAHA100693"

    start_date, method, confidence = pipeline._find_battery_start_with_confidence(
        battery_id, before_date, vin
    )

    print(f"\n" + "=" * 60)
    print("RESULT:")
    print(f"Found date: {start_date}")
    print(f"Before date constraint: {before_date}")
    print(f"Method: {method}")
    print()

    if start_date and start_date < before_date:
        print("✅ SUCCESS: Date validation works correctly!")
        print(
            f"   Found date ({start_date}) is properly BEFORE constraint ({before_date})"
        )
        print(f"   Events after {before_date} were correctly ignored")
    elif start_date and start_date >= before_date:
        print("❌ FAILURE: Date validation failed!")
        print(f"   Found date ({start_date}) is NOT before constraint ({before_date})")
    else:
        print("❌ FAILURE: No date found (unexpected)")


if __name__ == "__main__":
    test_date_validation()
