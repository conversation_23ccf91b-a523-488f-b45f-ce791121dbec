#!/usr/bin/env python3
"""
Test script to verify the enhanced temporal sequence logic.
Tests the specific case of battery 22912 where the snapshot battery came AFTER.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class MockPipelineForTemporalTest:
    """Mock pipeline to test temporal sequence logic."""

    def __init__(self):
        # Real data from battery 22912 case
        self.cleaned_events = [
            # Battery 22912 removal event (2020-08-27)
            {
                "date": datetime(2020, 8, 27),
                "vin": "WS5D16GAAJA701495",
                "battery_id_old": "22912",
                "battery_id_new": "20750",
                "action": "repaired",
            },
            # Battery 28449 installation event (2021-02-10) - AFTER 22912 removal
            {
                "date": datetime(2021, 2, 10),
                "vin": "WS5D16GAAJA701495",
                "battery_id_old": "20387",
                "battery_id_new": "28449",
                "action": "repaired",
            },
        ]

        self.vehicle_info_cache = {
            "WS5D16GAAJA701495": {
                "vin": "WS5D16GAAJA701495",
                "erstzulassung": datetime(2018, 5, 3),  # Vehicle rollout
                "master_battery": "28449",  # Current snapshot battery
                "slave_battery": None,
            }
        }

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Enhanced method with temporal sequence logic."""
        vehicle_info = self.vehicle_info_cache.get(vin, {})

        print(f"\n=== Testing temporal sequence for battery {battery_id} ===")
        print(f"Before date constraint: {before_date}")
        print(f"Vehicle erstzulassung: {vehicle_info.get('erstzulassung')}")
        print(f"Vehicle snapshot battery: {vehicle_info.get('master_battery')}")

        # Skip Priority 1-4 for this test (they would return None)

        # Priority 5: Direct snapshot matching
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        print(f"\nPriority 5 - Direct snapshot matching:")
        print(f"  Battery {battery_id} in snapshot? {battery_id in snapshot_batteries}")

        if battery_id in snapshot_batteries:
            erstzulassung = vehicle_info.get("erstzulassung")
            if erstzulassung and (not before_date or erstzulassung < before_date):
                return erstzulassung, "snapshot_match_erstzulassung", "high"

        # Priority 5B: Enhanced temporal sequence analysis
        print(f"\nPriority 5B - Temporal sequence analysis:")
        if battery_id not in snapshot_batteries and before_date:
            for snapshot_battery in snapshot_batteries:
                if snapshot_battery and pd.notna(snapshot_battery):
                    print(f"  Checking snapshot battery: {snapshot_battery}")

                    # Look for installation events of snapshot battery AFTER our before_date
                    snapshot_install_events = [
                        e
                        for e in self.cleaned_events
                        if e["battery_id_new"] == snapshot_battery
                        and e["vin"] == vin
                        and e["date"] > before_date  # AFTER our event
                    ]

                    print(
                        f"  Snapshot battery events after {before_date}: {len(snapshot_install_events)}"
                    )
                    for event in snapshot_install_events:
                        print(
                            f"    → {event['date']}: {event['battery_id_old']} → {event['battery_id_new']}"
                        )

                    if snapshot_install_events:
                        # Snapshot battery came AFTER our battery was removed
                        erstzulassung = vehicle_info.get("erstzulassung")
                        if erstzulassung and erstzulassung < before_date:
                            print(f"  ✅ TEMPORAL LOGIC APPLIED:")
                            print(
                                f"    - Snapshot battery {snapshot_battery} installed AFTER our event"
                            )
                            print(
                                f"    - Our battery {battery_id} likely started at erstzulassung"
                            )
                            print(f"    - Start date: {erstzulassung}")
                            return (
                                erstzulassung,
                                f"temporal_sequence_inference_from_erstzulassung_before_{snapshot_battery}",
                                "medium",
                            )

        return None, "no_start_found", "low"


def test_temporal_sequence_logic():
    """Test the temporal sequence logic for battery 22912."""

    print("TESTING ENHANCED TEMPORAL SEQUENCE LOGIC")
    print("=" * 70)
    print("Case: Battery 22912 in VIN WS5D16GAAJA701495")
    print("- Battery 22912 removed: 2020-08-27")
    print("- Snapshot battery 28449 installed: 2021-02-10 (AFTER)")
    print("- Vehicle rollout: 2018-05-03")
    print("- Expected: 22912 started at rollout (2018-05-03)")
    print()

    pipeline = MockPipelineForTemporalTest()

    # Test the specific case
    battery_id = "22912"
    before_date = datetime(2020, 8, 27)  # When 22912 was removed
    vin = "WS5D16GAAJA701495"

    start_date, method, confidence = pipeline._find_battery_start_with_confidence(
        battery_id, before_date, vin
    )

    print(f"\n" + "=" * 70)
    print("RESULT:")
    print(f"Battery ID: {battery_id}")
    print(f"Found start date: {start_date}")
    print(f"Method: {method}")
    print(f"Confidence: {confidence}")
    print()

    expected_date = datetime(2018, 5, 3)  # Vehicle rollout

    if start_date == expected_date and method.startswith("temporal_sequence_inference"):
        print("✅ SUCCESS: Temporal sequence logic worked!")
        print(
            f"   ✓ Correctly identified start date: {start_date.strftime('%Y-%m-%d')}"
        )
        print(f"   ✓ Used temporal sequence inference")
        print(f"   ✓ Medium confidence (logical inference)")
        print()
        print("LOGIC EXPLANATION:")
        print("   1. Battery 22912 doesn't match current snapshot (28449)")
        print("   2. Snapshot battery 28449 was installed AFTER 22912 removal")
        print("   3. Therefore, 22912 must have been active before 28449")
        print("   4. Most likely start: vehicle rollout date (2018-05-03)")
    else:
        print("❌ FAILURE: Temporal sequence logic didn't work as expected")
        print(f"   Expected: {expected_date}")
        print(f"   Got: {start_date}")


if __name__ == "__main__":
    test_temporal_sequence_logic()
