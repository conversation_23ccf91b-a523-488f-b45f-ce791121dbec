#!/usr/bin/env python3
"""
Test Database Connectivity for Battery Timeline Pipeline

This script tests PostgreSQL database connectivity before running the main pipeline.
It provides clear guidance on resolving connection issues.
"""

import os
import sys
from data_preparation_pipeline import BatteryTimelinePipeline


def test_database_connectivity():
    """Test database connectivity with detailed feedback."""
    print("BATTERY TIMELINE PIPELINE - DATABASE CONNECTIVITY TEST")
    print("=" * 70)

    # Initialize pipeline
    pipeline = BatteryTimelinePipeline(
        "hv_repair_2025-06-02b.csv",
        "comparison_results/working_matching_vehicles.csv",
        "comparison_results/working_unique_vehicles.csv",
    )

    # Print connection details
    print(f"Connection String: {pipeline.db_connection_string}")
    print()

    # Test connectivity
    try:
        print("Testing database connectivity...")
        is_connected = pipeline.check_database_connectivity()

        if is_connected:
            print("\n✅ DATABASE CONNECTIVITY TEST PASSED")
            print("✅ Pipeline is ready to run with full activity validation")
            return True
        else:
            print("\n❌ DATABASE CONNECTIVITY TEST FAILED")
            return False

    except Exception as e:
        print(f"\n❌ DATABASE CONNECTIVITY TEST FAILED: {e}")
        print("\nTROUBLESHOOTING STEPS:")
        print("1. Check if PostgreSQL server is running:")
        print("   sudo systemctl status postgresql")
        print("2. Check if the database port is accessible:")
        print(f"   telnet localhost 6543")
        print("3. Verify database credentials and permissions")
        print("4. Check environment variables:")
        for var in ["DB_HOST", "DB_PORT", "DB_NAME", "DB_USER", "DB_PASSWORD"]:
            value = os.getenv(var, "Not set")
            print(f"   {var}={value}")
        return False


def main():
    """Main function."""
    success = test_database_connectivity()

    if success:
        print("\n" + "=" * 70)
        print("READY TO RUN PIPELINE")
        print("=" * 70)
        print("Database connectivity verified. You can now run:")
        print("  python3 data_preparation_pipeline.py")
        print("=" * 70)
        sys.exit(0)
    else:
        print("\n" + "=" * 70)
        print("DATABASE CONNECTION REQUIRED")
        print("=" * 70)
        print("Please resolve database connectivity issues before running pipeline.")
        print("Alternatively, run in legacy mode without database validation:")
        print("  pipeline.run_analysis_without_db_validation()")
        print("=" * 70)
        sys.exit(1)


if __name__ == "__main__":
    main()
