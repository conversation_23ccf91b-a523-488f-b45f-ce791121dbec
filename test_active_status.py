#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced active status indicators for battery timelines.

This script shows how to distinguish between:
1. 🟢 CURRENTLY ACTIVE - Batteries that are truly active right now
2. ❓ MISSING END DATE (likely ended) - Batteries with missing data
3. ENDED (date) - Batteries with confirmed end dates
"""

from data_preparation_pipeline import BatteryTimelinePipeline
import pandas as pd
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def test_active_status_indicators():
    """Test the enhanced active status indicators."""
    
    print("🔋 BATTERY ACTIVE STATUS INDICATOR TEST")
    print("=" * 60)
    
    # Initialize pipeline with test batteries
    test_batteries = ["20400", "20561", "28965"]
    
    pipeline = BatteryTimelinePipeline(
        hv_repair_file="hv_repair_2025-06-02b.csv",
        working_matching_vehicles_file="comparison_results/working_matching_vehicles.csv",
        working_unique_vehicles_file="comparison_results/working_unique_vehicles.csv",
        daily_stats_csv_file="daily_stats.csv",
        test_batteries=test_batteries
    )
    
    try:
        print(f"🧪 Testing with batteries: {', '.join(test_batteries)}")
        print()
        
        # Run the analysis
        results = pipeline.run_analysis_with_db_check()
        
        if not results["success"]:
            print(f"❌ Analysis failed: {results.get('error')}")
            return
        
        # Export timeline with enhanced active status
        timeline_file = pipeline.export_timeline("test_active_status_timeline.csv")
        
        # Load and display the results
        df = pd.read_csv(timeline_file)
        
        print("📊 ACTIVE STATUS SUMMARY")
        print("-" * 40)
        
        # Count different statuses
        if "is_currently_active" in df.columns:
            active_count = df["is_currently_active"].sum()
            total_count = len(df)
            inactive_count = total_count - active_count
            
            print(f"Total periods: {total_count}")
            print(f"🟢 Currently active: {active_count}")
            print(f"❌ Ended/Missing data: {inactive_count}")
            print()
            
            # Show status distribution
            if "active_status" in df.columns:
                status_counts = df["active_status"].value_counts()
                print("📈 STATUS BREAKDOWN:")
                for status, count in status_counts.items():
                    print(f"  {status}: {count}")
                print()
        
        # Display sample records for each battery
        print("🔍 SAMPLE RECORDS BY BATTERY")
        print("-" * 40)
        
        for battery_id in test_batteries:
            battery_records = df[df["battery_id"] == battery_id]
            if not battery_records.empty:
                print(f"\n🔋 Battery {battery_id} ({len(battery_records)} periods):")
                
                # Show key columns
                display_cols = ["vin", "start_date", "end_date", "is_currently_active", "active_status", "lifecycle_stage"]
                available_cols = [col for col in display_cols if col in battery_records.columns]
                
                for _, record in battery_records.iterrows():
                    print(f"  VIN: {record['vin']}")
                    print(f"    Start: {record.get('start_date', 'N/A')}")
                    print(f"    End: {record.get('end_date', 'None')}")
                    if "is_currently_active" in record:
                        print(f"    Active: {record['is_currently_active']}")
                    if "active_status" in record:
                        print(f"    Status: {record['active_status']}")
                    if "lifecycle_stage" in record:
                        print(f"    Stage: {record['lifecycle_stage']}")
                    print()
        
        print("✅ INTERPRETATION GUIDE:")
        print("-" * 40)
        print("🟢 CURRENTLY ACTIVE = Battery is truly active right now")
        print("   - Vehicle has recent activity (last 60 days)")
        print("   - Battery appears in current vehicle snapshot")
        print("   - No conflicts with other active periods")
        print()
        print("❓ MISSING END DATE (likely ended) = Missing data, probably ended")
        print("   - No recent vehicle activity")
        print("   - Battery not in current snapshots")
        print("   - Algorithm couldn't determine end date")
        print()
        print("ENDED (date) = Battery period has confirmed end date")
        print("   - Clear end date from repair events or activity data")
        print()
        
        print(f"📁 Full results exported to: {timeline_file}")
        print("   Open this CSV to see all active status indicators!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

def show_csv_columns():
    """Show what the enhanced CSV columns look like."""
    print("\n📋 ENHANCED CSV COLUMNS")
    print("=" * 50)
    print("The exported CSV now includes these key columns:")
    print()
    print("🔋 battery_id - Battery identifier")
    print("🚗 vin - Vehicle identifier") 
    print("✅ is_currently_active - TRUE/FALSE for active status")
    print("📊 active_status - Human-readable status description")
    print("📅 start_date - Period start date")
    print("📅 end_date - Period end date (None if ongoing/missing)")
    print("🔄 lifecycle_stage - Battery lifecycle stage")
    print("📝 note - Additional details and explanations")
    print()
    print("💡 TIP: Sort by 'is_currently_active' column to see active batteries first!")

if __name__ == "__main__":
    test_active_status_indicators()
    show_csv_columns()