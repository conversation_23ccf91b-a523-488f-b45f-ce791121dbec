#!/usr/bin/env python3
"""
Unit tests for BatteryGrouper and date handling logic.

Tests battery period grouping with various scenarios, missing date handling
with different combinations, and edge cases like single periods and multiple transfers.

Requirements tested: 1.2, 1.3, 1.4, 1.5
"""

import unittest
import pandas as pd
from datetime import datetime, date
import sys
import os

# Add the current directory to Python path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from battery_age_calculator import BatteryGrouper, MissingDateHandler


class TestBatteryGrouper(unittest.TestCase):
    """Test cases for BatteryGrouper class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample timeline data for testing
        self.sample_data = pd.DataFrame([
            {
                'battery_id': 'BAT001',
                'vin': 'VIN001',
                'start_date': pd.Timestamp('2023-01-01'),
                'end_date': pd.Timestamp('2023-06-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT001',
                'vin': 'VIN002',
                'start_date': pd.Timestamp('2023-06-15'),
                'end_date': pd.Timestamp('2023-12-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT002',
                'vin': 'VIN003',
                'start_date': pd.Timestamp('2023-03-01'),
                'end_date': pd.Timestamp('2023-09-01'),
                'is_currently_active': True
            }
        ])
    
    def test_group_battery_timelines_basic(self):
        """Test basic battery timeline grouping functionality."""
        grouper = BatteryGrouper(self.sample_data)
        grouped = grouper.group_battery_timelines()
        
        # Should have 2 unique batteries
        self.assertEqual(len(grouped), 2)
        self.assertIn('BAT001', grouped)
        self.assertIn('BAT002', grouped)
        
        # BAT001 should have 2 periods
        bat001_info = grouped['BAT001']
        self.assertEqual(bat001_info['period_count'], 2)
        self.assertEqual(bat001_info['earliest_start'], pd.Timestamp('2023-01-01'))
        self.assertEqual(bat001_info['latest_end'], pd.Timestamp('2023-12-01'))
        
        # BAT002 should have 1 period
        bat002_info = grouped['BAT002']
        self.assertEqual(bat002_info['period_count'], 1)
        self.assertEqual(bat002_info['earliest_start'], pd.Timestamp('2023-03-01'))
        self.assertEqual(bat002_info['latest_end'], pd.Timestamp('2023-09-01'))
    
    def test_group_single_period_battery(self):
        """Test grouping for battery with single period."""
        single_period_data = pd.DataFrame([
            {
                'battery_id': 'BAT_SINGLE',
                'vin': 'VIN_SINGLE',
                'start_date': pd.Timestamp('2023-05-01'),
                'end_date': pd.Timestamp('2023-10-01'),
                'is_currently_active': False
            }
        ])
        
        grouper = BatteryGrouper(single_period_data)
        grouped = grouper.group_battery_timelines()
        
        self.assertEqual(len(grouped), 1)
        battery_info = grouped['BAT_SINGLE']
        self.assertEqual(battery_info['period_count'], 1)
        self.assertEqual(battery_info['earliest_start'], pd.Timestamp('2023-05-01'))
        self.assertEqual(battery_info['latest_end'], pd.Timestamp('2023-10-01'))
    
    def test_group_multiple_transfers(self):
        """Test grouping for battery with multiple vehicle transfers."""
        multiple_transfers_data = pd.DataFrame([
            {
                'battery_id': 'BAT_MULTI',
                'vin': 'VIN001',
                'start_date': pd.Timestamp('2023-01-01'),
                'end_date': pd.Timestamp('2023-03-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT_MULTI',
                'vin': 'VIN002',
                'start_date': pd.Timestamp('2023-03-15'),
                'end_date': pd.Timestamp('2023-06-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT_MULTI',
                'vin': 'VIN003',
                'start_date': pd.Timestamp('2023-06-10'),
                'end_date': pd.Timestamp('2023-09-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT_MULTI',
                'vin': 'VIN004',
                'start_date': pd.Timestamp('2023-09-15'),
                'end_date': pd.Timestamp('2023-12-01'),
                'is_currently_active': True
            }
        ])
        
        grouper = BatteryGrouper(multiple_transfers_data)
        grouped = grouper.group_battery_timelines()
        
        self.assertEqual(len(grouped), 1)
        battery_info = grouped['BAT_MULTI']
        self.assertEqual(battery_info['period_count'], 4)
        self.assertEqual(battery_info['earliest_start'], pd.Timestamp('2023-01-01'))
        self.assertEqual(battery_info['latest_end'], pd.Timestamp('2023-12-01'))
    
    def test_group_with_missing_dates(self):
        """Test grouping with missing start and end dates."""
        missing_dates_data = pd.DataFrame([
            {
                'battery_id': 'BAT_MISSING',
                'vin': 'VIN001',
                'start_date': pd.NaT,  # Missing start date
                'end_date': pd.Timestamp('2023-06-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT_MISSING',
                'vin': 'VIN002',
                'start_date': pd.Timestamp('2023-07-01'),
                'end_date': pd.NaT,  # Missing end date
                'is_currently_active': True
            }
        ])
        
        grouper = BatteryGrouper(missing_dates_data)
        grouped = grouper.group_battery_timelines()
        
        self.assertEqual(len(grouped), 1)
        battery_info = grouped['BAT_MISSING']
        self.assertEqual(battery_info['period_count'], 2)
        
        # With missing dates, min/max should handle NaT appropriately
        self.assertEqual(battery_info['earliest_start'], pd.Timestamp('2023-07-01'))
        self.assertEqual(battery_info['latest_end'], pd.Timestamp('2023-06-01'))
    
    def test_get_battery_aggregation(self):
        """Test retrieving specific battery aggregation."""
        grouper = BatteryGrouper(self.sample_data)
        grouped = grouper.group_battery_timelines()
        
        # Test existing battery
        bat001_info = grouper.get_battery_aggregation('BAT001')
        self.assertIsNotNone(bat001_info)
        self.assertEqual(bat001_info['period_count'], 2)
        
        # Test non-existing battery
        non_existing = grouper.get_battery_aggregation('BAT_NONEXISTENT')
        self.assertIsNone(non_existing)
    
    def test_get_batteries_with_missing_dates(self):
        """Test identification of batteries with missing dates."""
        missing_dates_data = pd.DataFrame([
            {
                'battery_id': 'BAT_MISSING_START',
                'vin': 'VIN001',
                'start_date': pd.NaT,
                'end_date': pd.Timestamp('2023-06-01'),
                'is_currently_active': False
            },
            {
                'battery_id': 'BAT_MISSING_END',
                'vin': 'VIN002',
                'start_date': pd.Timestamp('2023-01-01'),
                'end_date': pd.NaT,
                'is_currently_active': True
            },
            {
                'battery_id': 'BAT_COMPLETE',
                'vin': 'VIN003',
                'start_date': pd.Timestamp('2023-01-01'),
                'end_date': pd.Timestamp('2023-06-01'),
                'is_currently_active': False
            }
        ])
        
        grouper = BatteryGrouper(missing_dates_data)
        grouped = grouper.group_battery_timelines()
        missing_dates = grouper.get_batteries_with_missing_dates()
        
        self.assertIn('BAT_MISSING_START', missing_dates['missing_start'])
        self.assertIn('BAT_MISSING_END', missing_dates['missing_end'])
        self.assertNotIn('BAT_COMPLETE', missing_dates['missing_start'])
        self.assertNotIn('BAT_COMPLETE', missing_dates['missing_end'])
    
    def test_empty_dataframe(self):
        """Test grouping with empty DataFrame."""
        empty_data = pd.DataFrame(columns=['battery_id', 'vin', 'start_date', 'end_date', 'is_currently_active'])
        
        grouper = BatteryGrouper(empty_data)
        grouped = grouper.group_battery_timelines()
        
        self.assertEqual(len(grouped), 0)
        missing_dates = grouper.get_batteries_with_missing_dates()
        self.assertEqual(len(missing_dates['missing_start']), 0)
        self.assertEqual(len(missing_dates['missing_end']), 0)


class TestMissingDateHandler(unittest.TestCase):
    """Test cases for MissingDateHandler class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create grouped batteries data with missing dates for testing
        self.grouped_batteries_with_missing = {
            'BAT_MISSING_START': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_MISSING_START',
                        'vin': 'VIN001',
                        'start_date': pd.NaT,
                        'end_date': pd.Timestamp('2023-06-01'),
                        'is_currently_active': False
                    },
                    {
                        'battery_id': 'BAT_MISSING_START',
                        'vin': 'VIN002',
                        'start_date': pd.Timestamp('2023-07-01'),
                        'end_date': pd.Timestamp('2023-12-01'),
                        'is_currently_active': False
                    }
                ]),
                'earliest_start': pd.NaT,
                'latest_end': pd.Timestamp('2023-12-01'),
                'period_count': 2
            },
            'BAT_MISSING_END': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_MISSING_END',
                        'vin': 'VIN003',
                        'start_date': pd.Timestamp('2023-01-01'),
                        'end_date': pd.Timestamp('2023-05-01'),
                        'is_currently_active': False
                    },
                    {
                        'battery_id': 'BAT_MISSING_END',
                        'vin': 'VIN004',
                        'start_date': pd.Timestamp('2023-06-01'),
                        'end_date': pd.NaT,
                        'is_currently_active': True
                    }
                ]),
                'earliest_start': pd.Timestamp('2023-01-01'),
                'latest_end': pd.NaT,
                'period_count': 2
            }
        }
    
    def test_handle_missing_start_date(self):
        """Test handling of missing start dates using next available date."""
        handler = MissingDateHandler(self.grouped_batteries_with_missing)
        updated_batteries = handler.handle_missing_dates()
        
        # BAT_MISSING_START should now have earliest_start filled
        bat_info = updated_batteries['BAT_MISSING_START']
        self.assertEqual(bat_info['earliest_start'], pd.Timestamp('2023-07-01'))
        self.assertEqual(handler.date_fixes_applied['start_dates_fixed'], 1)
    
    def test_handle_missing_end_date(self):
        """Test handling of missing end dates using previous available date."""
        handler = MissingDateHandler(self.grouped_batteries_with_missing)
        updated_batteries = handler.handle_missing_dates()
        
        # BAT_MISSING_END should now have latest_end filled
        bat_info = updated_batteries['BAT_MISSING_END']
        self.assertEqual(bat_info['latest_end'], pd.Timestamp('2023-05-01'))
        self.assertEqual(handler.date_fixes_applied['end_dates_fixed'], 1)
    
    def test_handle_unfixable_missing_dates(self):
        """Test handling when no valid dates are available for fixing."""
        unfixable_batteries = {
            'BAT_NO_VALID_DATES': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_NO_VALID_DATES',
                        'vin': 'VIN001',
                        'start_date': pd.NaT,
                        'end_date': pd.NaT,
                        'is_currently_active': False
                    }
                ]),
                'earliest_start': pd.NaT,
                'latest_end': pd.NaT,
                'period_count': 1
            }
        }
        
        handler = MissingDateHandler(unfixable_batteries)
        updated_batteries = handler.handle_missing_dates()
        
        # Dates should remain NaT since no valid dates are available
        bat_info = updated_batteries['BAT_NO_VALID_DATES']
        self.assertTrue(pd.isna(bat_info['earliest_start']))
        self.assertTrue(pd.isna(bat_info['latest_end']))
        self.assertEqual(handler.date_fixes_applied['unfixable_start_dates'], 1)
        self.assertEqual(handler.date_fixes_applied['unfixable_end_dates'], 1)
    
    def test_validate_date_logic_valid_ranges(self):
        """Test date logic validation for valid date ranges."""
        valid_batteries = {
            'BAT_VALID': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_VALID',
                        'vin': 'VIN001',
                        'start_date': pd.Timestamp('2023-01-01'),
                        'end_date': pd.Timestamp('2023-06-01'),
                        'is_currently_active': False
                    }
                ]),
                'earliest_start': pd.Timestamp('2023-01-01'),
                'latest_end': pd.Timestamp('2023-06-01'),
                'period_count': 1
            }
        }
        
        handler = MissingDateHandler(valid_batteries)
        validation_results = handler.validate_date_logic()
        
        self.assertIn('BAT_VALID', validation_results['valid_ranges'])
        self.assertEqual(len(validation_results['invalid_ranges']), 0)
    
    def test_validate_date_logic_invalid_ranges(self):
        """Test date logic validation for invalid date ranges (end before start)."""
        invalid_batteries = {
            'BAT_INVALID': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_INVALID',
                        'vin': 'VIN001',
                        'start_date': pd.Timestamp('2023-06-01'),
                        'end_date': pd.Timestamp('2023-01-01'),
                        'is_currently_active': False
                    }
                ]),
                'earliest_start': pd.Timestamp('2023-06-01'),
                'latest_end': pd.Timestamp('2023-01-01'),
                'period_count': 1
            }
        }
        
        handler = MissingDateHandler(invalid_batteries)
        validation_results = handler.validate_date_logic()
        
        self.assertIn('BAT_INVALID', validation_results['invalid_ranges'])
        self.assertEqual(len(validation_results['valid_ranges']), 0)
    
    def test_multiple_periods_date_selection(self):
        """Test date selection logic with multiple periods having various date combinations."""
        complex_batteries = {
            'BAT_COMPLEX': {
                'records': pd.DataFrame([
                    {
                        'battery_id': 'BAT_COMPLEX',
                        'vin': 'VIN001',
                        'start_date': pd.Timestamp('2023-03-01'),  # Middle start date
                        'end_date': pd.Timestamp('2023-05-01'),    # Early end date
                        'is_currently_active': False
                    },
                    {
                        'battery_id': 'BAT_COMPLEX',
                        'vin': 'VIN002',
                        'start_date': pd.Timestamp('2023-01-01'),  # Earliest start date
                        'end_date': pd.Timestamp('2023-08-01'),    # Middle end date
                        'is_currently_active': False
                    },
                    {
                        'battery_id': 'BAT_COMPLEX',
                        'vin': 'VIN003',
                        'start_date': pd.Timestamp('2023-09-01'),  # Latest start date
                        'end_date': pd.Timestamp('2023-12-01'),    # Latest end date
                        'is_currently_active': False
                    }
                ]),
                'earliest_start': pd.NaT,  # Simulate missing aggregated start
                'latest_end': pd.NaT,      # Simulate missing aggregated end
                'period_count': 3
            }
        }
        
        handler = MissingDateHandler(complex_batteries)
        updated_batteries = handler.handle_missing_dates()
        
        # Should select earliest start (2023-01-01) and latest end (2023-12-01)
        bat_info = updated_batteries['BAT_COMPLEX']
        self.assertEqual(bat_info['earliest_start'], pd.Timestamp('2023-01-01'))
        self.assertEqual(bat_info['latest_end'], pd.Timestamp('2023-12-01'))


if __name__ == '__main__':
    unittest.main()