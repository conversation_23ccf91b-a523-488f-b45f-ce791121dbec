#!/usr/bin/env python3
"""
Test script to demonstrate improved validation handling for multiple daily_stats records.

This shows what happens when a single date has multiple records with mixed validation results.
"""

from data_preparation_pipeline import BatteryTimelinePipeline
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_multiple_records_scenario():
    """Test the improved validation with multiple records per date."""

    print("=" * 80)
    print("TESTING MULTIPLE DAILY_STATS RECORDS VALIDATION")
    print("=" * 80)

    # Initialize pipeline
    pipeline = BatteryTimelinePipeline(
        hv_repair_file="hv_repair_2025-06-02b.csv",
        working_matching_vehicles_file="comparison_results/working_matching_vehicles.csv",
        working_unique_vehicles_file="comparison_results/working_unique_vehicles.csv",
    )

    # Just initialize database connection without loading all data
    pipeline._initialize_database_connection()

    if not pipeline.db_engine:
        print("❌ No database connection available for testing")
        return

    # Load VIN to vehicle_id mapping
    pipeline._load_vehicle_activity_data()

    print("\n1. TESTING SCENARIO: Multiple Records Per Date")
    print("-" * 50)
    print("Scenario: Same date has 3 records - 2 pass validation, 1 fails")
    print("Expected: Date should be validated as ACTIVE (any valid record passes)")
    print()

    # Let's find a VIN that actually has multiple records on the same date
    from sqlalchemy import text

    try:
        # Find VINs with multiple records per date
        query = text(
            """
            SELECT v.vin, ds.date, COUNT(*) as record_count,
                   STRING_AGG(
                       CASE 
                           WHEN ds.km_end - ds.km_start >= 2 AND ds.km_end > ds.km_start AND ds.km_start >= 0 
                           THEN 'PASS(' || (ds.km_end - ds.km_start)::text || 'km)'
                           ELSE 'FAIL(' || COALESCE((ds.km_end - ds.km_start)::text, 'null') || 'km)'
                       END, 
                       ', '
                   ) as validation_results
            FROM public.vehicles v
            JOIN public.daily_stats ds ON v.vehicle_id = ds.vehicle_id
            WHERE ds.km_start IS NOT NULL AND ds.km_end IS NOT NULL
            GROUP BY v.vin, ds.date
            HAVING COUNT(*) >= 2
            ORDER BY record_count DESC, ds.date DESC
            LIMIT 5
        """
        )

        with pipeline.db_engine.connect() as conn:
            result = conn.execute(query)
            multiple_record_examples = result.fetchall()

            if multiple_record_examples:
                print("Found examples of dates with multiple records:")
                print("VIN                | Date       | Records | Validation Results")
                print("-" * 70)

                for row in multiple_record_examples:
                    vin, date, count, validation_results = row
                    print(f"{vin} | {date} | {count:7} | {validation_results}")

                # Test the improved validation on the first example
                test_vin = multiple_record_examples[0][0]
                test_date = datetime.combine(
                    multiple_record_examples[0][1], datetime.min.time()
                )

                print(f"\n2. TESTING IMPROVED VALIDATION")
                print("-" * 50)
                print(f"Testing VIN: {test_vin}")
                print(f"Testing Date: {test_date.date()}")

                # Test the improved validation
                result = pipeline._query_vehicle_activity_on_date(test_vin, test_date)

                if result:
                    print(f"\n✅ VALIDATION RESULT:")
                    print(f"  Date: {result['date']}")
                    print(f"  KM Start: {result['km_start']}")
                    print(f"  KM End: {result['km_end']}")
                    print(f"  Has Valid Activity: {result['has_km_activity']}")
                    print(f"  Total Records: {result['total_records']}")
                    print(f"  Valid Records: {result['valid_records']}")
                    print(f"  Details: {result['details']}")

                    if result["has_km_activity"]:
                        print(
                            f"\n🎉 SUCCESS: Date validated as ACTIVE because at least one record shows ≥2km movement"
                        )
                    else:
                        print(
                            f"\n⚠️  All records failed validation (no record shows ≥2km movement)"
                        )
                else:
                    print(f"\n❌ No validation result returned")

                # Show detailed breakdown of all records for this date
                print(f"\n3. DETAILED BREAKDOWN OF ALL RECORDS")
                print("-" * 50)

                detail_query = text(
                    """
                    SELECT timestamp_start, km_start, km_end, 
                           (km_end - km_start) as km_diff,
                           CASE 
                               WHEN km_end - km_start >= 2 AND km_end > km_start AND km_start >= 0 
                               THEN 'PASS' 
                               ELSE 'FAIL' 
                           END as validation_status
                    FROM public.daily_stats ds
                    JOIN public.vehicles v ON ds.vehicle_id = v.vehicle_id
                    WHERE v.vin = :vin AND ds.date = :date
                    ORDER BY ds.timestamp_start ASC
                """
                )

                vehicle_id = pipeline.vin_to_vehicle_id.get(test_vin)
                if vehicle_id:
                    with pipeline.db_engine.connect() as conn:
                        detail_result = conn.execute(
                            detail_query, {"vin": test_vin, "date": test_date.date()}
                        )
                        detail_rows = detail_result.fetchall()

                        print("Timestamp   | KM Start | KM End | KM Diff | Status")
                        print("-" * 55)
                        for detail_row in detail_rows:
                            ts, km_start, km_end, km_diff, status = detail_row
                            print(
                                f"{ts:>10} | {km_start:8} | {km_end:6} | {km_diff:7} | {status}"
                            )

            else:
                print("❌ No examples found of dates with multiple records")
                print("This might indicate that multiple records per date are rare,")
                print("or the test database doesn't have such cases.")

    except Exception as e:
        print(f"❌ Error during testing: {e}")

    print(f"\n4. TECHNICAL COMPARISON")
    print("-" * 50)
    print("OLD LOGIC (FLAWED):")
    print("  row = result.fetchone()  # Gets only first record")
    print("  if row and (km_end - km_start >= 2): validate_as_active()")
    print("  → Result depends on arbitrary database ordering")
    print()
    print("NEW LOGIC (ROBUST):")
    print("  rows = result.fetchall()  # Gets ALL records")
    print("  for each row: check if (km_end - km_start >= 2)")
    print("  if ANY row passes: validate_as_active()")
    print("  → Result is consistent regardless of record ordering")
    print()
    print("BENEFIT:")
    print("  ✅ Multiple valid activities on same date = Date validated")
    print("  ✅ Redundant validation improves accuracy")
    print("  ✅ More robust against data anomalies")


if __name__ == "__main__":
    test_multiple_records_scenario()
