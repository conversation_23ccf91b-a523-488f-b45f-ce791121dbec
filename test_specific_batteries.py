#!/usr/bin/env python3
"""
Quick test script for specific batteries to verify the approach works.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Set
import warnings
import psycopg2
from sqlalchemy import create_engine, text
import os

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Test batteries list
TEST_BATTERIES = [
    "V6P0116B000AA01438",
    "10459",
    "10001",
    "22912",
    "27514",
    "21194",
    "29307",
    "26769",
]


class QuickBatteryTest:
    def __init__(self):
        self.db_connection_string = (
            "postgresql://datadump:pAUjuLftyHURa5Ra@localhost:6543/LeitwartenDB"
        )
        self.db_engine = None
        self.vin_to_vehicle_id = {}
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.cleaned_events = []
        self.vehicle_info_cache = {}

    def initialize_database(self):
        """Initialize database connection."""
        try:
            self.db_engine = create_engine(self.db_connection_string)
            with self.db_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            self.db_engine = None

    def load_vin_mapping(self):
        """Load VIN to vehicle_id mapping."""
        if not self.db_engine:
            return

        try:
            mapping_query = (
                "SELECT vin, vehicle_id FROM public.vehicles WHERE vin IS NOT NULL"
            )
            mapping_df = pd.read_sql(mapping_query, self.db_engine)

            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Loaded VIN mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )
        except Exception as e:
            logger.error(f"Failed to load VIN mapping: {e}")

    def load_repair_data(self):
        """Load repair data."""
        logger.info("Loading repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")

        # Clean dates
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = (
                self.hv_repair_df[col]
                .astype(str)
                .replace(["nan", "", " ", "None"], None)
            )

        # Filter for test batteries only
        test_battery_events = self.hv_repair_df[
            (self.hv_repair_df["battery_id_old"].isin(TEST_BATTERIES))
            | (self.hv_repair_df["battery_id_new"].isin(TEST_BATTERIES))
        ]

        logger.info(f"Found {len(test_battery_events)} events for test batteries")

        # Clean events
        for _, row in test_battery_events.iterrows():
            event = {
                "date": row["effective_date"],
                "vin": row["vin"],
                "battery_id_old": (
                    row["battery_id_old"] if pd.notna(row["battery_id_old"]) else None
                ),
                "battery_id_new": (
                    row["battery_id_new"] if pd.notna(row["battery_id_new"]) else None
                ),
                "action": row["action"],
            }

            # Classify event type
            if event["battery_id_old"] and event["battery_id_new"]:
                event["event_type"] = "change"
            elif event["battery_id_new"] and not event["battery_id_old"]:
                event["event_type"] = "install"
            elif event["battery_id_old"] and not event["battery_id_new"]:
                event["event_type"] = "remove_or_confirm"
            else:
                event["event_type"] = "unclear"

            self.cleaned_events.append(event)

    def load_vehicle_data(self):
        """Load vehicle snapshot data."""
        logger.info("Loading vehicle data...")

        # Load both vehicle files
        matching_df = pd.read_csv("comparison_results/working_matching_vehicles.csv")
        unique_df = pd.read_csv("comparison_results/working_unique_vehicles.csv")
        self.working_vehicles_df = pd.concat(
            [matching_df, unique_df], ignore_index=True
        )

        # Clean data
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        # Build vehicle info cache
        for _, row in self.working_vehicles_df.iterrows():
            vin = row.get("vin")
            if vin and pd.notna(vin):
                self.vehicle_info_cache[vin] = {
                    "vin": vin,
                    "erstzulassung": row.get("erstzulassung"),
                    "master_battery": row.get("master"),
                    "slave_battery": row.get("slave"),
                }

        logger.info(f"Loaded vehicle data for {len(self.vehicle_info_cache)} vehicles")

    def query_vehicle_activity(self, vin: str, query_type: str = "summary"):
        """Query vehicle activity on-the-fly."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if query_type == "summary":
                query = text(
                    """
                SELECT 
                    COUNT(date) as total_days,
                    MIN(date) as first_activity_date,
                    MAX(date) as last_activity_date,
                    AVG(CASE 
                        WHEN km_end >= km_start AND km_end >= 0 AND km_start >= 0 
                        THEN km_end - km_start 
                        ELSE NULL 
                    END) as avg_daily_km
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id
                """
                )

                with self.db_engine.connect() as conn:
                    result = conn.execute(query, {"vehicle_id": vehicle_id})
                    row = result.fetchone()

                    if row:
                        return {
                            "total_days": row[0],
                            "first_activity_date": row[1],
                            "last_activity_date": row[2],
                            "avg_daily_km": row[3],
                        }
        except Exception as e:
            logger.debug(f"Error querying activity for {vin}: {e}")

        return None

    def build_battery_timeline(self, battery_id: str):
        """Build timeline for a specific battery."""
        logger.info(f"\n=== PROCESSING BATTERY {battery_id} ===")

        # Filter events for this battery
        battery_events = [
            event
            for event in self.cleaned_events
            if event.get("battery_id_old") == battery_id
            or event.get("battery_id_new") == battery_id
        ]

        if not battery_events:
            logger.info(f"No events found for battery {battery_id}")
            return []

        # Sort by descending date (latest first)
        battery_events.sort(key=lambda x: x["date"], reverse=True)

        logger.info(f"Found {len(battery_events)} events for battery {battery_id}")
        for event in battery_events:
            logger.info(
                f"  {event['date'].strftime('%Y-%m-%d')} | {event['vin']} | {event['event_type']} | old:{event['battery_id_old']} -> new:{event['battery_id_new']}"
            )

        periods = []
        processed_pairs = set()

        # Process events from latest to earliest
        for i, event in enumerate(battery_events):
            event_vin = event["vin"]
            event_date = event["date"]

            if event.get("battery_id_new") == battery_id:
                # Battery installation
                pair_id = f"install_{event_vin}_{event_date.isoformat()}"
                if pair_id in processed_pairs:
                    continue

                # Check if this is currently active (no later removal)
                is_currently_active = True
                end_date = None

                for j in range(i):  # Look at more recent events
                    if (
                        battery_events[j].get("battery_id_old") == battery_id
                        and battery_events[j]["vin"] == event_vin
                    ):
                        is_currently_active = False
                        end_date = battery_events[j]["date"]
                        break

                # Create period
                period = {
                    "battery_id": battery_id,
                    "vin": event_vin,
                    "start_date": event_date,
                    "end_date": end_date,
                    "lifecycle_stage": "active" if is_currently_active else "replaced",
                    "source": "repair_event",
                    "confidence": "high",
                }

                # Add activity validation
                activity = self.query_vehicle_activity(event_vin, "summary")
                if activity:
                    period["activity_days"] = activity["total_days"]
                    period["first_activity"] = activity["first_activity_date"]
                    period["avg_daily_km"] = activity["avg_daily_km"]

                periods.append(period)
                processed_pairs.add(pair_id)

                logger.info(
                    f"  -> Created period: {event_date.strftime('%Y-%m-%d')} to {'current' if is_currently_active else end_date.strftime('%Y-%m-%d')}"
                )

        # Check for snapshot-only presence
        for vin, vehicle_info in self.vehicle_info_cache.items():
            snapshot_batteries = [
                vehicle_info.get("master_battery"),
                vehicle_info.get("slave_battery"),
            ]
            if battery_id in snapshot_batteries:
                # Check if we already have a period for this VIN
                existing_vins = [p["vin"] for p in periods]
                if vin not in existing_vins:
                    erstzulassung = vehicle_info.get("erstzulassung")
                    if erstzulassung:
                        period = {
                            "battery_id": battery_id,
                            "vin": vin,
                            "start_date": erstzulassung,
                            "end_date": None,
                            "lifecycle_stage": "active",
                            "source": "snapshot_only",
                            "confidence": "medium",
                        }

                        # Add activity validation
                        activity = self.query_vehicle_activity(vin, "summary")
                        if activity:
                            period["activity_days"] = activity["total_days"]
                            period["first_activity"] = activity["first_activity_date"]
                            period["avg_daily_km"] = activity["avg_daily_km"]

                        periods.append(period)
                        logger.info(
                            f"  -> Added snapshot period: {erstzulassung.strftime('%Y-%m-%d')} to current"
                        )

        return periods

    def run_test(self):
        """Run the quick test."""
        logger.info("=== QUICK BATTERY TEST ===")

        # Initialize
        self.initialize_database()
        self.load_vin_mapping()
        self.load_repair_data()
        self.load_vehicle_data()

        # Process each test battery
        all_periods = []
        for battery_id in TEST_BATTERIES:
            periods = self.build_battery_timeline(battery_id)
            all_periods.extend(periods)

        # Export results
        if all_periods:
            df = pd.DataFrame(all_periods)
            df.to_csv("test_battery_results.csv", index=False)
            logger.info(f"\n✅ Results exported to test_battery_results.csv")
            logger.info(f"Total periods: {len(all_periods)}")

            # Show summary
            logger.info("\n=== SUMMARY ===")
            for battery_id in TEST_BATTERIES:
                battery_periods = [
                    p for p in all_periods if p["battery_id"] == battery_id
                ]
                if battery_periods:
                    logger.info(f"Battery {battery_id}: {len(battery_periods)} periods")
                    for period in battery_periods:
                        start = (
                            period["start_date"].strftime("%Y-%m-%d")
                            if period["start_date"]
                            else "unknown"
                        )
                        end = (
                            period["end_date"].strftime("%Y-%m-%d")
                            if period["end_date"]
                            else "current"
                        )
                        logger.info(
                            f"  {period['vin']}: {start} -> {end} ({period['source']})"
                        )
                else:
                    logger.info(f"Battery {battery_id}: No periods found")

        return all_periods


if __name__ == "__main__":
    test = QuickBatteryTest()
    test.run_test()
