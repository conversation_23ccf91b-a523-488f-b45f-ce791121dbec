#!/usr/bin/env python3
"""
Test script to verify the Vehicle Battery History Analysis enhancement.
Tests the specific case of battery 27514 where it's likely the original battery.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class MockPipelineForHistoryTest:
    """Mock pipeline to test vehicle battery history logic."""

    def __init__(self):
        # Real data from VIN WS5D16JAAKA100074
        self.cleaned_events = [
            # Battery 27514 event (what we're analyzing)
            {
                "date": datetime(2020, 9, 25),
                "vin": "WS5D16JAAKA100074",
                "battery_id_old": "27514",
                "battery_id_new": None,  # Empty - maintenance event
                "action": "changed",
            },
            # Later events (AFTER our event)
            {
                "date": datetime(2021, 6, 28),
                "vin": "WS5D16JAAKA100074",
                "battery_id_old": "21194",
                "battery_id_new": "29307",
                "action": "repaired",
            },
            {
                "date": datetime(2021, 8, 19),
                "vin": "WS5D16JAAKA100074",
                "battery_id_old": "29307",
                "battery_id_new": "26769",
                "action": "changed",
            },
            {
                "date": datetime(2024, 2, 6),
                "vin": "WS5D16JAAKA100074",
                "battery_id_old": "26769",
                "battery_id_new": None,
                "action": "changed",
            },
        ]

        self.vehicle_info_cache = {
            "WS5D16JAAKA100074": {
                "vin": "WS5D16JAAKA100074",
                "erstzulassung": datetime(2019, 7, 29),  # Vehicle rollout
                "master_battery": "26769",  # Current snapshot battery
                "slave_battery": None,
            }
        }

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Enhanced method with vehicle history analysis."""
        vehicle_info = self.vehicle_info_cache.get(vin, {})

        print(f"\n=== Testing vehicle history analysis for battery {battery_id} ===")
        print(f"VIN: {vin}")
        print(f"Before date constraint: {before_date}")
        print(f"Vehicle erstzulassung: {vehicle_info.get('erstzulassung')}")
        print(f"Vehicle snapshot battery: {vehicle_info.get('master_battery')}")

        # Skip Priority 1-5B for this test (they would return None)

        # Priority 5C: Vehicle Battery History Analysis
        print(f"\nPriority 5C - Vehicle Battery History Analysis:")
        if before_date:
            # Get all battery events for this VIN
            vin_all_events = [e for e in self.cleaned_events if e["vin"] == vin]
            print(f"  Total events for VIN: {len(vin_all_events)}")

            # Look for any battery events BEFORE our before_date that involve OTHER batteries
            earlier_battery_events = []
            print(f"  Checking for other batteries before {before_date}:")

            for event in vin_all_events:
                if event["date"] < before_date:
                    print(
                        f"    Event {event['date']}: {event['battery_id_old']} → {event['battery_id_new']}"
                    )

                    # Check if this event involves a different battery
                    other_batteries = []
                    if (
                        event["battery_id_old"]
                        and event["battery_id_old"] != battery_id
                    ):
                        other_batteries.append(event["battery_id_old"])
                        print(
                            f"      → Different battery in old field: {event['battery_id_old']}"
                        )
                    if (
                        event["battery_id_new"]
                        and event["battery_id_new"] != battery_id
                    ):
                        other_batteries.append(event["battery_id_new"])
                        print(
                            f"      → Different battery in new field: {event['battery_id_new']}"
                        )

                    if other_batteries:
                        earlier_battery_events.append(event)
                        print(
                            f"      ✓ Found earlier battery event with other batteries"
                        )
                    else:
                        print(f"      - No other batteries in this event")

            print(f"  Earlier battery events found: {len(earlier_battery_events)}")

            # If no other batteries found before our event, this is likely the original battery
            if not earlier_battery_events:
                erstzulassung = vehicle_info.get("erstzulassung")
                if erstzulassung and erstzulassung < before_date:
                    print(f"  ✅ VEHICLE HISTORY LOGIC APPLIED:")
                    print(f"    - No other batteries found before {before_date}")
                    print(f"    - Vehicle must have had a battery since rollout")
                    print(
                        f"    - Battery {battery_id} likely original since {erstzulassung}"
                    )
                    return (
                        erstzulassung,
                        "original_battery_no_earlier_events",
                        "medium",
                    )
                else:
                    print(f"  ❌ Erstzulassung validation failed")
            else:
                print(f"  ❌ Other batteries found before our event")

        return None, "no_start_found", "low"


def test_vehicle_history_logic():
    """Test the vehicle battery history logic for battery 27514."""

    print("TESTING VEHICLE BATTERY HISTORY ANALYSIS")
    print("=" * 70)
    print("Case: Battery 27514 in VIN WS5D16JAAKA100074")
    print("- Vehicle rollout: 2019-07-29")
    print("- Battery 27514 maintenance event: 2020-09-25")
    print("- No earlier battery events found")
    print("- Expected: 27514 was original battery since rollout")
    print()

    pipeline = MockPipelineForHistoryTest()

    # Test the specific case
    battery_id = "27514"
    before_date = datetime(2020, 9, 25)  # When 27514 had maintenance
    vin = "WS5D16JAAKA100074"

    start_date, method, confidence = pipeline._find_battery_start_with_confidence(
        battery_id, before_date, vin
    )

    print(f"\n" + "=" * 70)
    print("RESULT:")
    print(f"Battery ID: {battery_id}")
    print(f"Found start date: {start_date}")
    print(f"Method: {method}")
    print(f"Confidence: {confidence}")
    print()

    expected_date = datetime(2019, 7, 29)  # Vehicle rollout

    if start_date == expected_date and method == "original_battery_no_earlier_events":
        print("✅ SUCCESS: Vehicle battery history logic worked!")
        print(
            f"   ✓ Correctly identified start date: {start_date.strftime('%Y-%m-%d')}"
        )
        print(f"   ✓ Used vehicle history analysis")
        print(f"   ✓ Medium confidence (logical inference)")
        print()
        print("LOGIC EXPLANATION:")
        print("   1. No battery events found before 2020-09-25")
        print("   2. Vehicle cannot operate without a battery")
        print("   3. Therefore, 27514 was likely the original battery")
        print("   4. Start date: vehicle rollout (2019-07-29)")
        print("   5. 2020-09-25 event was maintenance, not removal")
    else:
        print("❌ FAILURE: Vehicle history logic didn't work as expected")
        print(
            f"   Expected: {expected_date} with method 'original_battery_no_earlier_events'"
        )
        print(f"   Got: {start_date} with method '{method}'")


if __name__ == "__main__":
    test_vehicle_history_logic()
