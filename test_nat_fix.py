#!/usr/bin/env python3
"""
Quick test script to verify NaT handling fixes
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from data_preparation_pipeline import BatteryTimelinePipeline

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_nat_handling():
    """Test that the pipeline can handle NaT values without crashing."""

    # Create a simple pipeline instance (without actually loading data)
    pipeline = BatteryTimelinePipeline(
        "dummy.csv", "dummy.csv", "dummy.csv", "dummy.csv"
    )

    # Test _determine_battery_status with various problematic inputs
    test_cases = [
        (None, "active", "high"),
        (pd.NaT, "active", "medium"),
        (np.nan, "removed", "low"),
        (datetime.now(), "removed", "high"),
    ]

    print("Testing _determine_battery_status with various inputs:")
    for end_date, lifecycle_stage, confidence in test_cases:
        try:
            result = pipeline._determine_battery_status(
                end_date, lifecycle_stage, confidence
            )
            print(f"  ✅ {end_date} ({type(end_date)}) -> {result}")
        except Exception as e:
            print(f"  ❌ {end_date} ({type(end_date)}) -> ERROR: {e}")

    # Test _determine_active_status
    print("\nTesting _determine_active_status with various inputs:")
    for end_date in [None, pd.NaT, np.nan, datetime.now()]:
        try:
            result = pipeline._determine_active_status(end_date)
            print(f"  ✅ {end_date} ({type(end_date)}) -> {result}")
        except Exception as e:
            print(f"  ❌ {end_date} ({type(end_date)}) -> ERROR: {e}")

    # Test _create_period with NaT values
    print("\nTesting _create_period with NaT values:")
    try:
        period = pipeline._create_period(
            "test_battery",
            "test_vin",
            pd.NaT,  # start_date as NaT
            datetime.now(),  # end_date as normal datetime
            "test",
            "medium",
            "active",
            note="Test period",
        )
        print(f"  ✅ Created period with NaT start_date: {period['duration_days']}")
    except Exception as e:
        print(f"  ❌ Failed to create period with NaT start_date: {e}")

    try:
        period = pipeline._create_period(
            "test_battery",
            "test_vin",
            datetime.now(),  # start_date as normal datetime
            pd.NaT,  # end_date as NaT
            "test",
            "medium",
            "active",
            note="Test period",
        )
        print(f"  ✅ Created period with NaT end_date: {period['duration_days']}")
    except Exception as e:
        print(f"  ❌ Failed to create period with NaT end_date: {e}")


if __name__ == "__main__":
    test_nat_handling()
