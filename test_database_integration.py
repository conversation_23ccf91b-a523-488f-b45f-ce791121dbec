#!/usr/bin/env python3
"""
Integration tests for database connectivity.

Tests VIN to vehicle_id mapping functionality, daily stats querying with mock data,
and graceful handling of database connection failures.

Requirements tested: 2.2, 2.3, 2.4, 2.5
"""

import unittest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import sys
from sqlalchemy.exc import SQLAlchemyError

# Add the current directory to Python path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from battery_age_calculator import DatabaseConnector, DatabaseConnectionError


class TestDatabaseConnector(unittest.TestCase):
    """Test cases for DatabaseConnector class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary CSV file for daily stats testing
        self.temp_daily_stats_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        self.daily_stats_csv_path = self.temp_daily_stats_file.name
        
        # Write sample daily stats data
        sample_daily_stats = """vehicle_id,date,km_start,km_end
1,2023-01-01,1000.0,1050.0
1,2023-01-02,1050.0,1100.0
1,2023-01-03,1100.0,1150.0
2,2023-01-01,2000.0,2080.0
2,2023-01-02,2080.0,2160.0
3,2023-01-01,3000.0,3030.0
"""
        self.temp_daily_stats_file.write(sample_daily_stats)
        self.temp_daily_stats_file.close()
        
        # Mock connection string
        self.mock_connection_string = "postgresql://user:pass@localhost:5432/testdb"
    
    def tearDown(self):
        """Clean up test fixtures after each test method."""
        # Remove temporary file
        if os.path.exists(self.daily_stats_csv_path):
            os.unlink(self.daily_stats_csv_path)
    
    def test_init_with_connection_string(self):
        """Test DatabaseConnector initialization with connection string."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        self.assertEqual(connector.connection_string, self.mock_connection_string)
        self.assertEqual(connector.daily_stats_csv_path, self.daily_stats_csv_path)
        self.assertFalse(connector.is_connected)
        self.assertEqual(len(connector.vin_mappings), 0)
    
    @patch('battery_age_calculator.create_engine')
    def test_connect_success(self, mock_create_engine):
        """Test successful database connection."""
        # Mock successful connection
        mock_engine = Mock()
        mock_connection = MagicMock()
        mock_result = Mock()
        mock_result.fetchone.return_value = (1,)
        mock_connection.execute.return_value = mock_result
        mock_engine.connect.return_value = mock_connection
        mock_create_engine.return_value = mock_engine
        
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        result = connector.connect()
        
        self.assertTrue(result)
        self.assertTrue(connector.is_connected)
        mock_create_engine.assert_called_once_with(self.mock_connection_string)
    
    @patch('battery_age_calculator.create_engine')
    def test_connect_failure(self, mock_create_engine):
        """Test database connection failure."""
        # Mock connection failure
        mock_create_engine.side_effect = SQLAlchemyError("Connection failed")
        
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        result = connector.connect()
        
        self.assertFalse(result)
        self.assertFalse(connector.is_connected)
    
    @patch('battery_age_calculator.create_engine')
    def test_disconnect(self, mock_create_engine):
        """Test database disconnection."""
        # Mock successful connection first
        mock_engine = Mock()
        mock_connection = MagicMock()
        mock_result = Mock()
        mock_result.fetchone.return_value = (1,)
        mock_connection.execute.return_value = mock_result
        mock_engine.connect.return_value = mock_connection
        mock_create_engine.return_value = mock_engine
        
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Connect first
        connector.connect()
        self.assertTrue(connector.is_connected)
        
        # Then disconnect
        connector.disconnect()
        self.assertFalse(connector.is_connected)
        mock_engine.dispose.assert_called_once()
    
    def test_load_vin_mappings_no_connection(self):
        """Test VIN mapping when database is not connected."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Mock connect method to fail
        with patch.object(connector, 'connect', return_value=False):
            with self.assertRaises(DatabaseConnectionError):
                connector.load_vin_mappings(['VIN001'])
    
    def test_load_daily_stats_csv_success(self):
        """Test successful loading of daily stats from CSV."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        connector.load_daily_stats_csv()
        
        # Verify data was loaded
        self.assertIsNotNone(connector.daily_stats_df)
        self.assertEqual(len(connector.daily_stats_df), 6)  # 6 rows in sample data
        
        # Verify pre-indexing by vehicle_id
        self.assertEqual(len(connector.daily_stats_by_vehicle), 3)  # 3 unique vehicles
        self.assertIn(1, connector.daily_stats_by_vehicle)
        self.assertIn(2, connector.daily_stats_by_vehicle)
        self.assertIn(3, connector.daily_stats_by_vehicle)
        
        # Verify vehicle 1 has 3 records
        vehicle_1_data = connector.daily_stats_by_vehicle[1]
        self.assertEqual(len(vehicle_1_data), 3)
    
    def test_load_daily_stats_csv_file_not_found(self):
        """Test daily stats loading with non-existent file."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path="non_existent_file.csv"
        )
        
        with self.assertRaises(Exception):
            connector.load_daily_stats_csv()
    
    def test_get_latest_daily_stats_date_success(self):
        """Test getting latest daily stats date for a vehicle."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        # Test vehicle 1 (has 3 records, latest should be 2023-01-03)
        latest_date = connector.get_latest_daily_stats_date(1)
        self.assertEqual(latest_date, pd.Timestamp('2023-01-03'))
        
        # Test vehicle 2 (has 2 records, latest should be 2023-01-02)
        latest_date = connector.get_latest_daily_stats_date(2)
        self.assertEqual(latest_date, pd.Timestamp('2023-01-02'))
        
        # Test vehicle 3 (has 1 record, should be 2023-01-01)
        latest_date = connector.get_latest_daily_stats_date(3)
        self.assertEqual(latest_date, pd.Timestamp('2023-01-01'))
    
    def test_get_latest_daily_stats_date_not_found(self):
        """Test getting latest daily stats date for non-existent vehicle."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        # Test non-existent vehicle
        latest_date = connector.get_latest_daily_stats_date(999)
        self.assertIsNone(latest_date)
    
    def test_get_latest_daily_stats_dates_batch(self):
        """Test batch querying of latest daily stats dates."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        vehicle_ids = [1, 2, 3, 999]  # Include non-existent vehicle
        date_mappings = connector.get_latest_daily_stats_dates_batch(vehicle_ids)
        
        expected_mappings = {
            1: pd.Timestamp('2023-01-03'),
            2: pd.Timestamp('2023-01-02'),
            3: pd.Timestamp('2023-01-01'),
            999: None
        }
        
        self.assertEqual(date_mappings, expected_mappings)
    
    def test_get_latest_daily_stats_dates_batch_empty_list(self):
        """Test batch querying with empty vehicle list."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        date_mappings = connector.get_latest_daily_stats_dates_batch([])
        self.assertEqual(date_mappings, {})
    
    def test_get_latest_daily_stats_for_vin_success(self):
        """Test getting latest daily stats for a VIN (combines VIN mapping and daily stats)."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        # Mock VIN mapping directly
        connector.vin_mappings = {'VIN001': 1}
        
        # Mock get_vehicle_id_for_vin to return cached mapping
        with patch.object(connector, 'get_vehicle_id_for_vin', return_value=1):
            latest_date = connector.get_latest_daily_stats_for_vin('VIN001')
            
            # Should return latest date for vehicle_id 1
            self.assertEqual(latest_date, pd.Timestamp('2023-01-03'))
    
    def test_get_latest_daily_stats_for_vin_not_found(self):
        """Test getting latest daily stats for VIN that doesn't exist."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        # Mock get_vehicle_id_for_vin to return None (VIN not found)
        with patch.object(connector, 'get_vehicle_id_for_vin', return_value=None):
            latest_date = connector.get_latest_daily_stats_for_vin('VIN_NONEXISTENT')
            self.assertIsNone(latest_date)
    
    def test_get_latest_daily_stats_for_vins_batch(self):
        """Test batch querying of latest daily stats for multiple VINs."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        connector.load_daily_stats_csv()
        
        # Mock load_vin_mappings to return partial mappings
        mock_mappings = {'VIN001': 1, 'VIN002': 2}  # VIN003 not found
        
        with patch.object(connector, 'load_vin_mappings', return_value=mock_mappings):
            vins = ['VIN001', 'VIN002', 'VIN003']
            vin_date_mappings = connector.get_latest_daily_stats_for_vins_batch(vins)
            
            expected_mappings = {
                'VIN001': pd.Timestamp('2023-01-03'),
                'VIN002': pd.Timestamp('2023-01-02'),
                'VIN003': None  # Not found in VIN mapping
            }
            
            self.assertEqual(vin_date_mappings, expected_mappings)
    
    @patch('battery_age_calculator.create_engine')
    def test_test_connection_success(self, mock_create_engine):
        """Test connection testing functionality."""
        # Mock successful connection test
        mock_engine = Mock()
        mock_connection = MagicMock()
        mock_result = Mock()
        mock_result.fetchone.return_value = (1,)
        mock_connection.execute.return_value = mock_result
        mock_engine.connect.return_value = mock_connection
        mock_create_engine.return_value = mock_engine
        
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        result = connector.test_connection()
        
        self.assertTrue(result)
        mock_engine.dispose.assert_called_once()  # Should dispose test engine
    
    @patch('battery_age_calculator.create_engine')
    def test_test_connection_failure(self, mock_create_engine):
        """Test connection testing with failure."""
        # Mock connection failure
        mock_create_engine.side_effect = SQLAlchemyError("Connection failed")
        
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        result = connector.test_connection()
        
        self.assertFalse(result)
    
    def test_graceful_error_handling_daily_stats_not_loaded(self):
        """Test graceful handling when daily stats CSV is not loaded."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Don't load daily stats CSV
        
        # Should return None gracefully
        latest_date = connector.get_latest_daily_stats_date(1)
        self.assertIsNone(latest_date)
        
        # Batch query should return None for all vehicles
        date_mappings = connector.get_latest_daily_stats_dates_batch([1, 2, 3])
        expected = {1: None, 2: None, 3: None}
        self.assertEqual(date_mappings, expected)
    
    def test_get_vehicle_id_for_vin_cached(self):
        """Test getting vehicle_id for VIN from cache."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Pre-populate cache
        connector.vin_mappings = {'VIN001': 1, 'VIN002': 2}
        
        # Should return from cache
        vehicle_id = connector.get_vehicle_id_for_vin('VIN001')
        self.assertEqual(vehicle_id, 1)
        
        vehicle_id = connector.get_vehicle_id_for_vin('VIN002')
        self.assertEqual(vehicle_id, 2)
        
        # Non-existent VIN should trigger database lookup
        with patch.object(connector, 'load_vin_mappings', return_value={}):
            vehicle_id = connector.get_vehicle_id_for_vin('VIN003')
            self.assertIsNone(vehicle_id)
    
    def test_database_connection_error_handling(self):
        """Test graceful handling of database connection errors."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Mock database connection failure
        with patch.object(connector, 'load_vin_mappings', side_effect=DatabaseConnectionError("DB Error")):
            vehicle_id = connector.get_vehicle_id_for_vin('VIN001')
            self.assertIsNone(vehicle_id)
    
    def test_vin_mapping_integration_with_mocked_database(self):
        """Test VIN mapping functionality with mocked database responses."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        # Mock successful database connection and query
        mock_engine = Mock()
        mock_connection = MagicMock()
        
        # Create mock row objects for VIN mapping results
        mock_row1 = Mock()
        mock_row1.vin = 'VIN001'
        mock_row1.vehicle_id = 1
        
        mock_row2 = Mock()
        mock_row2.vin = 'VIN002'
        mock_row2.vehicle_id = 2
        
        mock_connection.execute.return_value = [mock_row1, mock_row2]
        mock_engine.connect.return_value = mock_connection
        
        # Set up connector state
        connector.is_connected = True
        connector.engine = mock_engine
        
        # Test VIN mapping - mock the load_vin_mappings method directly
        expected_mappings = {
            'VIN001': 1,
            'VIN002': 2
            # VIN003 not found
        }
        
        with patch.object(connector, 'load_vin_mappings', return_value=expected_mappings):
            vins = ['VIN001', 'VIN002', 'VIN003']
            mappings = connector.load_vin_mappings(vins)
            
            self.assertEqual(mappings, expected_mappings)
            self.assertEqual(len(mappings), 2)  # Only 2 out of 3 VINs found
    
    def test_daily_stats_csv_data_integrity(self):
        """Test that daily stats CSV data is loaded and indexed correctly."""
        connector = DatabaseConnector(
            connection_string=self.mock_connection_string,
            daily_stats_csv_path=self.daily_stats_csv_path
        )
        
        connector.load_daily_stats_csv()
        
        # Verify data types
        self.assertEqual(connector.daily_stats_df['vehicle_id'].dtype, 'int64')
        self.assertEqual(connector.daily_stats_df['km_start'].dtype, 'float64')
        self.assertEqual(connector.daily_stats_df['km_end'].dtype, 'float64')
        
        # Verify date parsing
        self.assertTrue(pd.api.types.is_datetime64_any_dtype(connector.daily_stats_df['date']))
        
        # Verify indexing structure
        for vehicle_id in [1, 2, 3]:
            self.assertIn(vehicle_id, connector.daily_stats_by_vehicle)
            vehicle_data = connector.daily_stats_by_vehicle[vehicle_id]
            self.assertTrue(vehicle_data['date'].is_monotonic_increasing)  # Should be sorted by date
    
    def test_error_handling_with_malformed_csv(self):
        """Test error handling with malformed daily stats CSV."""
        # Create malformed CSV
        malformed_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        malformed_csv.write("invalid,csv,structure\n1,2,3,4,5,6,7,8,9")  # Too many columns
        malformed_csv.close()
        
        try:
            connector = DatabaseConnector(
                connection_string=self.mock_connection_string,
                daily_stats_csv_path=malformed_csv.name
            )
            
            # Should raise an exception due to malformed CSV
            with self.assertRaises(Exception):
                connector.load_daily_stats_csv()
        
        finally:
            # Clean up
            os.unlink(malformed_csv.name)


if __name__ == '__main__':
    unittest.main()