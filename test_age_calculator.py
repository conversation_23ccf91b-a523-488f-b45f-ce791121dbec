#!/usr/bin/env python3
"""
Unit tests for AgeCalculator and validation logic.

Tests age calculation with normal date ranges, error handling for invalid date ranges,
leap year handling and decimal precision.

Requirements tested: 3.1, 3.2, 3.4, 3.5
"""

import unittest
import pandas as pd
from datetime import datetime, date
import sys
import os

# Add the current directory to Python path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from battery_age_calculator import AgeCalculator, BatteryAge


class TestAgeCalculator(unittest.TestCase):
    """Test cases for AgeCalculator class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.calculator = AgeCalculator()
    
    def test_calculate_age_days_normal_range(self):
        """Test age calculation with normal date ranges."""
        start_date = pd.Timestamp('2023-01-01')
        end_date = pd.Timestamp('2023-12-31')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT001')
        
        # Should be 364 days (2023 is not a leap year)
        self.assertEqual(age_days, 364.0)
        self.assertEqual(error_note, "")
        self.assertEqual(self.calculator.calculation_stats['successful_calculations'], 1)
    
    def test_calculate_age_days_leap_year(self):
        """Test leap year handling and decimal precision."""
        # 2024 is a leap year
        start_date = pd.Timestamp('2024-01-01')
        end_date = pd.Timestamp('2024-12-31')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT002')
        
        # Should be 365 days (2024 is a leap year, so Dec 31 - Jan 1 = 365 days)
        self.assertEqual(age_days, 365.0)
        self.assertEqual(error_note, "")
    
    def test_calculate_age_days_same_day(self):
        """Test edge case of same-day periods."""
        same_date = pd.Timestamp('2023-06-15')
        
        age_days, error_note = self.calculator.calculate_age_days(same_date, same_date, 'BAT003')
        
        self.assertEqual(age_days, 0.0)
        self.assertEqual(error_note, "")
    
    def test_calculate_age_days_one_day_difference(self):
        """Test calculation with one day difference."""
        start_date = pd.Timestamp('2023-06-15')
        end_date = pd.Timestamp('2023-06-16')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT004')
        
        self.assertEqual(age_days, 1.0)
        self.assertEqual(error_note, "")
    
    def test_calculate_age_days_missing_start_date(self):
        """Test error handling for missing start date."""
        end_date = pd.Timestamp('2023-12-31')
        
        # Test with None
        age_days, error_note = self.calculator.calculate_age_days(None, end_date, 'BAT005')
        self.assertIsNone(age_days)
        self.assertEqual(error_note, "Missing start date - cannot calculate age")
        
        # Test with pandas NaT
        age_days, error_note = self.calculator.calculate_age_days(pd.NaT, end_date, 'BAT006')
        self.assertIsNone(age_days)
        self.assertEqual(error_note, "Missing start date - cannot calculate age")
    
    def test_calculate_age_days_missing_end_date(self):
        """Test error handling for missing end date."""
        start_date = pd.Timestamp('2023-01-01')
        
        # Test with None
        age_days, error_note = self.calculator.calculate_age_days(start_date, None, 'BAT007')
        self.assertIsNone(age_days)
        self.assertEqual(error_note, "Missing end date - cannot calculate age")
        
        # Test with pandas NaT
        age_days, error_note = self.calculator.calculate_age_days(start_date, pd.NaT, 'BAT008')
        self.assertIsNone(age_days)
        self.assertEqual(error_note, "Missing end date - cannot calculate age")
    
    def test_calculate_age_days_invalid_date_range(self):
        """Test error handling for invalid date ranges (end before start)."""
        start_date = pd.Timestamp('2023-12-31')
        end_date = pd.Timestamp('2023-01-01')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT009')
        
        self.assertIsNone(age_days)
        self.assertIn("Invalid date range", error_note)
        self.assertIn("end date (2023-01-01) before start date (2023-12-31)", error_note)
        self.assertEqual(self.calculator.calculation_stats['validation_errors'], 1)
    
    def test_calculate_age_days_invalid_date_types(self):
        """Test error handling for invalid date types."""
        # Test with string instead of timestamp
        age_days, error_note = self.calculator.calculate_age_days("2023-01-01", pd.Timestamp('2023-12-31'), 'BAT010')
        self.assertIsNone(age_days)
        self.assertIn("Invalid start date type", error_note)
        
        # Test with integer instead of timestamp
        age_days, error_note = self.calculator.calculate_age_days(pd.Timestamp('2023-01-01'), 20231231, 'BAT011')
        self.assertIsNone(age_days)
        self.assertIn("Invalid end date type", error_note)
    
    def test_calculate_age_days_decimal_precision(self):
        """Test decimal precision rounding to 2 decimal places."""
        # Create dates that would result in fractional days if we used hours/minutes
        start_date = pd.Timestamp('2023-01-01')
        end_date = pd.Timestamp('2023-01-15')  # 14 days
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT012')
        
        self.assertEqual(age_days, 14.0)
        self.assertEqual(error_note, "")
        # Verify it's rounded to 2 decimal places (even though this example is a whole number)
        self.assertIsInstance(age_days, float)
    
    def test_calculate_age_days_very_large_age(self):
        """Test handling of very large age calculations."""
        # Test with dates 60 years apart (should trigger warning but still calculate)
        start_date = pd.Timestamp('1960-01-01')
        end_date = pd.Timestamp('2023-01-01')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT013')
        
        # Should still calculate but log a warning
        expected_days = (end_date - start_date).days
        self.assertEqual(age_days, float(expected_days))
        self.assertEqual(error_note, "")
    
    def test_calculate_battery_age_complete(self):
        """Test complete battery age calculation with BatteryAge object."""
        start_date = pd.Timestamp('2023-01-01')
        end_date = pd.Timestamp('2023-06-01')
        
        battery_age = self.calculator.calculate_battery_age('BAT014', start_date, end_date)
        
        self.assertIsInstance(battery_age, BatteryAge)
        self.assertEqual(battery_age.battery_id, 'BAT014')
        self.assertEqual(battery_age.earliest_start, start_date)
        self.assertEqual(battery_age.latest_end, end_date)
        self.assertEqual(battery_age.age_days, 151.0)  # Jan 1 to Jun 1 = 151 days
        self.assertEqual(battery_age.note, "")
    
    def test_calculate_battery_age_with_error(self):
        """Test complete battery age calculation with error case."""
        start_date = pd.Timestamp('2023-06-01')
        end_date = pd.Timestamp('2023-01-01')  # Invalid range
        
        battery_age = self.calculator.calculate_battery_age('BAT015', start_date, end_date)
        
        self.assertIsInstance(battery_age, BatteryAge)
        self.assertEqual(battery_age.battery_id, 'BAT015')
        self.assertEqual(battery_age.earliest_start, start_date)
        self.assertEqual(battery_age.latest_end, end_date)
        self.assertIsNone(battery_age.age_days)
        self.assertIn("Invalid date range", battery_age.note)
    
    def test_validate_dates_valid_range(self):
        """Test date validation for valid date ranges."""
        start_date = pd.Timestamp('2023-01-01')
        end_date = pd.Timestamp('2023-12-31')
        
        is_valid, error_message = self.calculator.validate_dates(start_date, end_date)
        
        self.assertTrue(is_valid)
        self.assertEqual(error_message, "")
    
    def test_validate_dates_none_values(self):
        """Test date validation for None values."""
        # Test None start date
        is_valid, error_message = self.calculator.validate_dates(None, pd.Timestamp('2023-12-31'))
        self.assertFalse(is_valid)
        self.assertEqual(error_message, "Start date is None")
        
        # Test None end date
        is_valid, error_message = self.calculator.validate_dates(pd.Timestamp('2023-01-01'), None)
        self.assertFalse(is_valid)
        self.assertEqual(error_message, "End date is None")
    
    def test_validate_dates_nat_values(self):
        """Test date validation for pandas NaT values."""
        # Test NaT start date
        is_valid, error_message = self.calculator.validate_dates(pd.NaT, pd.Timestamp('2023-12-31'))
        self.assertFalse(is_valid)
        self.assertEqual(error_message, "Start date is NaT (Not-a-Time)")
        
        # Test NaT end date
        is_valid, error_message = self.calculator.validate_dates(pd.Timestamp('2023-01-01'), pd.NaT)
        self.assertFalse(is_valid)
        self.assertEqual(error_message, "End date is NaT (Not-a-Time)")
    
    def test_validate_dates_invalid_types(self):
        """Test date validation for invalid date types."""
        # Test string date
        is_valid, error_message = self.calculator.validate_dates("2023-01-01", pd.Timestamp('2023-12-31'))
        self.assertFalse(is_valid)
        self.assertIn("Invalid start date type", error_message)
        
        # Test integer date
        is_valid, error_message = self.calculator.validate_dates(pd.Timestamp('2023-01-01'), 20231231)
        self.assertFalse(is_valid)
        self.assertIn("Invalid end date type", error_message)
    
    def test_validate_dates_invalid_range(self):
        """Test date validation for invalid date ranges."""
        start_date = pd.Timestamp('2023-12-31')
        end_date = pd.Timestamp('2023-01-01')
        
        is_valid, error_message = self.calculator.validate_dates(start_date, end_date)
        
        self.assertFalse(is_valid)
        self.assertIn("End date (2023-01-01) is before start date (2023-12-31)", error_message)
    
    def test_validate_dates_unreasonably_large_age(self):
        """Test date validation for unreasonably large age ranges."""
        start_date = pd.Timestamp('1900-01-01')
        end_date = pd.Timestamp('2023-01-01')
        
        is_valid, error_message = self.calculator.validate_dates(start_date, end_date)
        
        self.assertFalse(is_valid)
        self.assertIn("Unreasonably large age", error_message)
        self.assertIn(">50 years", error_message)
    
    def test_calculation_stats_tracking(self):
        """Test calculation statistics tracking."""
        # Reset stats
        self.calculator.reset_stats()
        
        # Perform various calculations
        self.calculator.calculate_age_days(pd.Timestamp('2023-01-01'), pd.Timestamp('2023-12-31'), 'BAT_SUCCESS')
        self.calculator.calculate_age_days(None, pd.Timestamp('2023-12-31'), 'BAT_MISSING')
        self.calculator.calculate_age_days(pd.Timestamp('2023-12-31'), pd.Timestamp('2023-01-01'), 'BAT_INVALID')
        
        stats = self.calculator.get_calculation_stats()
        
        self.assertEqual(stats['successful_calculations'], 1)
        self.assertEqual(stats['missing_date_errors'], 1)
        self.assertEqual(stats['validation_errors'], 1)
        self.assertEqual(stats['calculation_errors'], 0)
    
    def test_reset_stats(self):
        """Test statistics reset functionality."""
        # Perform a calculation to generate stats
        self.calculator.calculate_age_days(pd.Timestamp('2023-01-01'), pd.Timestamp('2023-12-31'), 'BAT_TEST')
        
        # Verify stats exist
        stats_before = self.calculator.get_calculation_stats()
        self.assertGreater(stats_before['successful_calculations'], 0)
        
        # Reset stats
        self.calculator.reset_stats()
        
        # Verify stats are reset
        stats_after = self.calculator.get_calculation_stats()
        self.assertEqual(stats_after['successful_calculations'], 0)
        self.assertEqual(stats_after['validation_errors'], 0)
        self.assertEqual(stats_after['missing_date_errors'], 0)
        self.assertEqual(stats_after['calculation_errors'], 0)
    
    def test_different_date_types_compatibility(self):
        """Test compatibility with different date types (datetime, date, pd.Timestamp)."""
        # Test with datetime objects
        start_datetime = datetime(2023, 1, 1)
        end_datetime = datetime(2023, 12, 31)
        
        age_days, error_note = self.calculator.calculate_age_days(start_datetime, end_datetime, 'BAT_DATETIME')
        self.assertEqual(age_days, 364.0)
        self.assertEqual(error_note, "")
        
        # Test with date objects
        start_date = date(2023, 1, 1)
        end_date = date(2023, 12, 31)
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT_DATE')
        self.assertEqual(age_days, 364.0)
        self.assertEqual(error_note, "")
        
        # Test with pd.Timestamp objects (already tested in other methods)
        start_timestamp = pd.Timestamp('2023-01-01')
        end_timestamp = pd.Timestamp('2023-12-31')
        
        age_days, error_note = self.calculator.calculate_age_days(start_timestamp, end_timestamp, 'BAT_TIMESTAMP')
        self.assertEqual(age_days, 364.0)
        self.assertEqual(error_note, "")
    
    def test_february_leap_year_calculation(self):
        """Test specific leap year calculation for February."""
        # Test leap year February (2024)
        start_date = pd.Timestamp('2024-02-01')
        end_date = pd.Timestamp('2024-03-01')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT_LEAP_FEB')
        
        # February 2024 has 29 days, so Feb 1 to Mar 1 = 29 days
        self.assertEqual(age_days, 29.0)
        self.assertEqual(error_note, "")
        
        # Test non-leap year February (2023)
        start_date = pd.Timestamp('2023-02-01')
        end_date = pd.Timestamp('2023-03-01')
        
        age_days, error_note = self.calculator.calculate_age_days(start_date, end_date, 'BAT_NON_LEAP_FEB')
        
        # February 2023 has 28 days, so Feb 1 to Mar 1 = 28 days
        self.assertEqual(age_days, 28.0)
        self.assertEqual(error_note, "")


if __name__ == '__main__':
    unittest.main()