#!/usr/bin/env python3
"""
Test script for daily stats functionality of DatabaseConnector.
"""

import logging
import sys
from battery_age_calculator import DatabaseConnector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_daily_stats():
    """Test the daily stats functionality."""
    logger.info("=== Testing Daily Stats Functionality ===")
    
    try:
        with DatabaseConnector() as db_connector:
            # Test single vehicle daily stats
            test_vehicle_id = 13804  # From previous test
            latest_date = db_connector.get_latest_daily_stats_date(test_vehicle_id)
            logger.info(f"Latest daily stats for vehicle_id {test_vehicle_id}: {latest_date}")
            
            # Test batch daily stats
            test_vehicle_ids = [13804, 21758]
            batch_dates = db_connector.get_latest_daily_stats_dates_batch(test_vehicle_ids)
            logger.info(f"Batch daily stats: {batch_dates}")
            
            # Test VIN-based daily stats
            test_vin = "WS5D16GAAJA100879"
            vin_date = db_connector.get_latest_daily_stats_for_vin(test_vin)
            logger.info(f"Latest daily stats for VIN {test_vin}: {vin_date}")
            
            # Test batch VIN daily stats
            test_vins = ["WS5D16GAAJA100879", "WS5D16JAAKA800003"]
            batch_vin_dates = db_connector.get_latest_daily_stats_for_vins_batch(test_vins)
            logger.info(f"Batch VIN daily stats: {batch_vin_dates}")
        
        return True
        
    except Exception as e:
        logger.error(f"Daily stats test failed: {str(e)}")
        return False

def main():
    """Run daily stats tests."""
    logger.info("Starting daily stats tests")
    
    if test_daily_stats():
        logger.info("Daily stats tests passed!")
        return 0
    else:
        logger.error("Daily stats tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())