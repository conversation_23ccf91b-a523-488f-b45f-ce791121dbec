#!/usr/bin/env python3
"""
Test script to verify the fix for battery 22405 overlapping periods issue.
"""

import pandas as pd
from data_preparation_pipeline import BatteryTimelinePipeline


def test_battery_22405_fix():
    """Test that battery 22405 no longer has overlapping periods."""

    print("Testing Battery 22405 Overlap Fix")
    print("=" * 50)

    # Initialize pipeline with test battery
    hv_repair_file = "hv_repair_2025-06-02b.csv"
    working_matching_vehicles_file = "comparison_results/working_matching_vehicles.csv"
    working_unique_vehicles_file = "comparison_results/working_unique_vehicles.csv"
    daily_stats_csv_file = "daily_stats.csv"

    # Test only battery 22405
    test_batteries = ["22405"]

    pipeline = BatteryTimelinePipeline(
        hv_repair_file,
        working_matching_vehicles_file,
        working_unique_vehicles_file,
        daily_stats_csv_file,
        test_batteries=test_batteries,
    )

    print(f"🧪 Testing battery: {test_batteries[0]}")
    print()

    try:
        # Run analysis
        results = pipeline.run_analysis_with_db_check()

        if not results["success"]:
            print(f"❌ Analysis failed: {results.get('error')}")
            return False

        # Get battery 22405 periods
        battery_22405_periods = [
            period
            for period in pipeline.final_timeline
            if period["battery_id"] == "22405"
        ]

        print(f"Found {len(battery_22405_periods)} periods for battery 22405:")
        print()

        # Display periods
        for i, period in enumerate(battery_22405_periods, 1):
            print(f"Period {i}:")
            print(f"  VIN: {period['vin']}")
            print(
                f"  Start: {period['start_date'].date() if period['start_date'] else 'None'}"
            )
            print(
                f"  End: {period['end_date'].date() if period['end_date'] else 'Ongoing'}"
            )
            print(f"  Source: {period['source']}")
            print(f"  Phase: {period['phase_created']}")
            print(f"  Note: {period.get('note', 'N/A')}")
            print()

        # Check for overlaps
        overlaps = []
        for i in range(len(battery_22405_periods)):
            for j in range(i + 1, len(battery_22405_periods)):
                period1 = battery_22405_periods[i]
                period2 = battery_22405_periods[j]

                # Check if they overlap
                start1 = period1["start_date"]
                end1 = period1["end_date"] or pd.Timestamp.now()
                start2 = period2["start_date"]
                end2 = period2["end_date"] or pd.Timestamp.now()

                if start1 and start2 and start1 < end2 and start2 < end1:
                    overlap_days = (min(end1, end2) - max(start1, start2)).days
                    if overlap_days > 0:
                        overlaps.append(
                            {
                                "period1": i + 1,
                                "period2": j + 1,
                                "overlap_days": overlap_days,
                                "vin1": period1["vin"],
                                "vin2": period2["vin"],
                            }
                        )

        # Report results
        if overlaps:
            print("❌ OVERLAPS DETECTED:")
            for overlap in overlaps:
                print(
                    f"  Period {overlap['period1']} ({overlap['vin1']}) overlaps with Period {overlap['period2']} ({overlap['vin2']}) by {overlap['overlap_days']} days"
                )
            print()
            return False
        else:
            print("✅ NO OVERLAPS DETECTED - Fix successful!")
            print()

        # Check Phase 4 statistics
        phase4_stats = results.get("phase_stats", {}).get("phase4", {})
        battery_conflicts_resolved = phase4_stats.get("battery_conflicts_resolved", 0)

        print(f"Phase 4 Statistics:")
        print(f"  Battery conflicts resolved: {battery_conflicts_resolved}")
        print(
            f"  Early battery assignments created: {phase4_stats.get('early_battery_assignments_created', 0)}"
        )
        print(
            f"  Timeline extensions created: {phase4_stats.get('timeline_extensions_created', 0)}"
        )

        # Check specific validation for battery 22405 timeline
        print(f"\nBattery 22405 Timeline Validation:")

        # Sort periods by start date for chronological analysis
        sorted_periods = sorted(
            battery_22405_periods,
            key=lambda x: x["start_date"] if x["start_date"] else pd.Timestamp.min,
        )

        for i, period in enumerate(sorted_periods, 1):
            start_str = period["start_date"].date() if period["start_date"] else "None"
            end_str = period["end_date"].date() if period["end_date"] else "Ongoing"
            print(f"  Period {i}: {start_str} to {end_str} in {period['vin']}")

        # Expected correct timeline:
        # Period 1: 2018-06-05 to 2020-12-01 (WS5D16GAAJA100248)
        # Period 2: 2020-12-01 to 2021-01-27 (WS5D16HAAJA102178)
        # Period 3: 2021-01-27 to 2022-09-23 (WS5D16GAAJA100248)
        # Period 4: 2022-09-23 to ongoing (WS5D16GAAJA100248)

        expected_timeline_valid = True
        if len(sorted_periods) >= 2:
            # Check if periods are properly sequential without overlaps
            for i in range(len(sorted_periods) - 1):
                current_period = sorted_periods[i]
                next_period = sorted_periods[i + 1]

                current_end = current_period["end_date"]
                next_start = next_period["start_date"]

                if current_end and next_start and current_end > next_start:
                    print(
                        f"    ❌ Overlap detected: Period {i+1} ends {current_end.date()} after Period {i+2} starts {next_start.date()}"
                    )
                    expected_timeline_valid = False
                elif current_end and next_start and current_end == next_start:
                    print(
                        f"    ✅ Perfect handoff: Period {i+1} ends exactly when Period {i+2} starts ({current_end.date()})"
                    )
                elif current_end and next_start:
                    gap_days = (next_start - current_end).days
                    if gap_days <= 7:  # Small gaps are acceptable
                        print(
                            f"    ✅ Small gap: {gap_days} days between Period {i+1} and Period {i+2}"
                        )
                    else:
                        print(
                            f"    ⚠️  Large gap: {gap_days} days between Period {i+1} and Period {i+2}"
                        )

        if expected_timeline_valid:
            print("    ✅ Timeline chronology is valid")
        else:
            print("    ❌ Timeline chronology has issues")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


if __name__ == "__main__":
    success = test_battery_22405_fix()
    if success:
        print("\n🎉 Battery 22405 overlap fix verification PASSED!")
    else:
        print("\n💥 Battery 22405 overlap fix verification FAILED!")
