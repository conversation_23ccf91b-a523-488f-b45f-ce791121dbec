#!/usr/bin/env python3
"""
Quick integration test for data loading components.
"""

import logging
from battery_age_calculator import TimelineLoader, DatabaseConnector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Quick integration test."""
    logger.info("=== Quick Integration Test ===")
    
    # Test TimelineLoader
    try:
        loader = TimelineLoader("days_calculate/battery_timeline.csv")
        data = loader.load_timeline_data()
        logger.info(f"✓ TimelineLoader: Loaded {len(data)} records")
        
        # Get sample VINs for testing
        sample_vins = data['vin'].dropna().head(3).tolist()
        logger.info(f"Sample VINs: {sample_vins}")
        
    except Exception as e:
        logger.error(f"✗ TimelineLoader failed: {str(e)}")
        return
    
    # Test DatabaseConnector
    try:
        db_connector = DatabaseConnector()
        if db_connector.test_connection():
            logger.info("✓ DatabaseConnector: Connection test passed")
            
            # Test VIN mapping
            mappings = db_connector.load_vin_mappings(sample_vins)
            logger.info(f"✓ VIN Mappings: {len(mappings)} mapped")
            
        else:
            logger.warning("✗ DatabaseConnector: Connection test failed")
            
    except Exception as e:
        logger.error(f"✗ DatabaseConnector failed: {str(e)}")
        return
    
    logger.info("=== Integration test completed ===")

if __name__ == "__main__":
    main()