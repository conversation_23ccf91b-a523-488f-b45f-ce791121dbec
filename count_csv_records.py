#!/usr/bin/env python3
"""
Simple script to count total records in daily_stats_sample.csv
"""

import pandas as pd

def count_csv_records(filename="daily_stats_sample.csv"):
    """Count total records in CSV file."""
    try:
        # Read CSV file
        df = pd.read_csv(filename)
        
        # Get record count
        total_records = len(df)
        
        print(f"📊 CSV File: {filename}")
        print(f"📈 Total records: {total_records:,}")
        print(f"📋 Columns: {list(df.columns)}")
        
        return total_records
        
    except FileNotFoundError:
        print(f"❌ File '{filename}' not found")
        return 0
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return 0

if __name__ == "__main__":
    count_csv_records()