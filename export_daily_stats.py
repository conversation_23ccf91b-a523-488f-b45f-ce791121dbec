#!/usr/bin/env python3
"""
Export daily_stats table to CSV file
"""

import pandas as pd
from sqlalchemy import create_engine, text
import os
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Database connection from config
from config import DB_CONNECTION_STRING


def export_daily_stats(limit=None, output_file=None):
    """Export daily_stats table to CSV file."""

    # Generate default output filename with timestamp if not provided
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"daily_stats_export_{timestamp}.csv"

    try:
        # Create database connection
        logger.info(f"Connecting to database...")
        engine = create_engine(DB_CONNECTION_STRING)

        # Build query
        if limit:
            logger.info(f"Exporting {limit} records from daily_stats table...")
            query = f"""
            SELECT vehicle_id, date, km_start, km_end FROM public.daily_stats 
            ORDER BY vehicle_id, date 
            LIMIT {limit}
            """
        else:
            logger.info("Exporting all records from daily_stats table...")
            query = "SELECT vehicle_id, date, km_start, km_end FROM public.daily_stats ORDER BY vehicle_id, date"

        # Execute query and load into DataFrame
        df = pd.read_sql(query, engine)

        # Export to CSV
        df.to_csv(output_file, index=False)
        logger.info(f"✅ Successfully exported to {output_file}")
        logger.info(f"   Total records: {len(df):,}")

        return output_file

    except Exception as e:
        logger.error(f"❌ Export failed: {e}")
        return None


def export_daily_stats_copy(limit=None, output_file=None):
    """Export using PostgreSQL COPY command - most memory efficient."""

    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"daily_stats_export_{timestamp}.csv"

    try:
        logger.info(f"Connecting to database...")
        engine = create_engine(DB_CONNECTION_STRING)

        # Build COPY query
        if limit:
            copy_query = f"""
            COPY (
                SELECT vehicle_id, date, km_start, km_end FROM public.daily_stats 
                ORDER BY vehicle_id, date 
                LIMIT {limit}
            ) TO STDOUT WITH CSV HEADER
            """
        else:
            copy_query = "COPY (SELECT vehicle_id, date, km_start, km_end FROM public.daily_stats) TO STDOUT WITH CSV HEADER"

        # Execute COPY command and write directly to file
        raw_conn = engine.raw_connection()
        try:
            with raw_conn.cursor() as cursor:
                with open(output_file, "w") as f:
                    cursor.copy_expert(copy_query, f)
        finally:
            raw_conn.close()

        logger.info(f"✅ Successfully exported to {output_file}")
        return output_file

    except Exception as e:
        logger.error(f"❌ Export failed: {e}")
        return None


if __name__ == "__main__":
    # Export 10 records for testing
    export_daily_stats_copy(limit=None, output_file="daily_stats_sample.csv")

    # Uncomment to export all records
    # export_daily_stats(output_file="daily_stats_full.csv")
