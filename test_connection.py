#!/usr/bin/env python3
"""
Test PostgreSQL connection for battery analysis
"""

import psycopg2
from sqlalchemy import create_engine, text
import pandas as pd
import sys


def test_basic_connection():
    """Test basic psycopg2 connection."""
    print("1. Testing basic psycopg2 connection...")

    try:
        conn = psycopg2.connect(
            host="localhost",
            port="5437",
            database="LeitwartenDB",
            user="datadump",
            password="pAUjuLftyHURa5Ra",
        )

        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ Basic connection successful!")
        print(f"   PostgreSQL version: {version[0]}")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ Basic connection failed: {e}")
        return False


def test_sqlalchemy_connection():
    """Test SQLAlchemy connection."""
    print("\n2. Testing SQLAlchemy connection...")

    connection_string = (
        "postgresql://datadump:pAUjuLftyHURa5Ra@localhost:5437/LeitwartenDB"
    )

    try:
        engine = create_engine(connection_string)

        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ SQLAlchemy connection successful!")

        return engine

    except Exception as e:
        print(f"❌ SQLAlchemy connection failed: {e}")
        print(f"   Connection string: {connection_string}")
        return None


def test_table_access(engine):
    """Test access to specific tables."""
    print("\n3. Testing table access...")

    if engine is None:
        print("❌ Skipping table tests - no engine available")
        return

    tables_to_test = ["vehicles", "daily_stats"]

    for table in tables_to_test:
        try:
            with engine.connect() as conn:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"✅ Table '{table}': {count:,} records")

        except Exception as e:
            print(f"❌ Table '{table}' failed: {e}")


def test_pandas_integration(engine):
    """Test pandas integration."""
    print("\n4. Testing pandas integration...")

    if engine is None:
        print("❌ Skipping pandas tests - no engine available")
        return

    try:
        # Test simple query
        df = pd.read_sql("SELECT COUNT(*) as total FROM vehicles", engine)
        print(f"✅ Pandas integration successful!")
        print(f"   Vehicles count via pandas: {df['total'].iloc[0]:,}")

        # Test more complex query
        query = """
        SELECT 
            COUNT(*) as total_records,
            MIN(date) as earliest_date,
            MAX(date) as latest_date
        FROM daily_stats 
        WHERE km_end IS NOT NULL
        """
        df_stats = pd.read_sql(query, engine)
        print(f"✅ Complex query successful!")
        print(f"   Daily stats: {df_stats['total_records'].iloc[0]:,} records")
        print(
            f"   Date range: {df_stats['earliest_date'].iloc[0]} to {df_stats['latest_date'].iloc[0]}"
        )

    except Exception as e:
        print(f"❌ Pandas integration failed: {e}")


def test_battery_analysis_prerequisites(engine):
    """Test specific requirements for battery analysis."""
    print("\n5. Testing battery analysis prerequisites...")

    if engine is None:
        print("❌ Skipping analysis tests - no engine available")
        return

    try:
        # Test vehicles table structure
        vehicles_query = """
        SELECT 
            COUNT(*) as total_vehicles,
            COUNT(DISTINCT vin) as unique_vins,
            COUNT(vehicle_id) as vehicles_with_id
        FROM vehicles
        """
        df_vehicles = pd.read_sql(vehicles_query, engine)
        print(f"✅ Vehicles analysis ready:")
        print(f"   Total vehicles: {df_vehicles['total_vehicles'].iloc[0]:,}")
        print(f"   Unique VINs: {df_vehicles['unique_vins'].iloc[0]:,}")

        # Test daily_stats for km data
        stats_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(km_end) as records_with_km,
            MIN(km_end) as min_km,
            MAX(km_end) as max_km
        FROM daily_stats
        """
        df_stats = pd.read_sql(stats_query, engine)
        print(f"✅ Daily stats analysis ready:")
        print(f"   Total records: {df_stats['total_records'].iloc[0]:,}")
        print(f"   Records with km_end: {df_stats['records_with_km'].iloc[0]:,}")
        print(
            f"   Km range: {df_stats['min_km'].iloc[0]:,.0f} - {df_stats['max_km'].iloc[0]:,.0f}"
        )

        # Test vehicle-stats join
        join_query = """
        SELECT COUNT(*) as joinable_records
        FROM vehicles v 
        INNER JOIN daily_stats ds ON v.vehicle_id = ds.vehicle_id
        WHERE ds.km_end IS NOT NULL
        """
        df_join = pd.read_sql(join_query, engine)
        print(f"✅ Vehicle-stats join ready:")
        print(f"   Joinable records: {df_join['joinable_records'].iloc[0]:,}")

    except Exception as e:
        print(f"❌ Analysis prerequisites failed: {e}")


def diagnose_connection_issues():
    """Diagnose common connection issues."""
    print("\n6. Diagnosing potential issues...")

    import socket

    # Test if port is accessible
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("localhost", 5437))
        sock.close()

        if result == 0:
            print("✅ Port 5437 is accessible")
        else:
            print("❌ Port 5437 is not accessible")
            print("   💡 Check if Docker container is running: docker ps")

    except Exception as e:
        print(f"❌ Port test failed: {e}")

    # Check for common issues
    print("\n   Common troubleshooting steps:")
    print("   1. Ensure Docker container is running: docker-compose up -d")
    print("   2. Check container status: docker ps | grep postgres-battery")
    print("   3. Check container logs: docker logs postgres-battery")
    print("   4. Verify port mapping: docker port postgres-battery")
    print(
        "   5. Test direct connection: docker exec -it postgres-battery psql -U datadump -d LeitwartenDB"
    )


def main():
    """Run all connection tests."""
    print("POSTGRESQL CONNECTION DIAGNOSTICS")
    print("=" * 50)

    # Test basic connection
    basic_ok = test_basic_connection()

    # Test SQLAlchemy
    engine = test_sqlalchemy_connection()

    # Test table access
    test_table_access(engine)

    # Test pandas integration
    test_pandas_integration(engine)

    # Test battery analysis requirements
    test_battery_analysis_prerequisites(engine)

    # Diagnose issues if needed
    if not basic_ok or engine is None:
        diagnose_connection_issues()

    print(f"\n{'='*50}")
    if basic_ok and engine is not None:
        print("🎉 All tests passed! Ready for battery analysis.")
    else:
        print("⚠️  Some tests failed. Check the diagnostics above.")


if __name__ == "__main__":
    main()
