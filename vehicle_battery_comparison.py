#!/usr/bin/env python3
"""
Vehicle-Battery Excel Comparison Tool

This script compares two Excel files:
1. "Fahrzeug Batterie Zuordnung-with-batteryid.xlsx"
2. "Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx"

Extracts vehicles and batteries with similar AKZ and VIN, then compares:
1. Erstzulassung dates consistency
2. Battery types (HV Batterie Bei E18 Master vs Slave)

Author: Battery Analysis Tool
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional, Set
import warnings
from pathlib import Path

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VehicleBatteryComparator:
    """Compare vehicle-battery data between two Excel files."""

    def __init__(self, file1_path: str, file2_path: str):
        """Initialize with paths to both Excel files."""
        self.file1_path = file1_path
        self.file2_path = file2_path
        self.df1 = None  # with-batteryid file
        self.df2 = None  # with-first-zulassung file
        self.df1_name = "battery-id"
        self.df2_name = "first-zulassung"

        # Analysis results
        self.common_vehicles = None
        self.date_comparison = None
        self.battery_comparison = None
        self.summary_stats = {}

    def load_excel_files(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load both Excel files and examine their structure."""
        logger.info("Loading Excel files...")

        try:
            # Load first file
            logger.info(f"Loading: {self.file1_path}")
            self.df1 = pd.read_excel(self.file1_path)
            logger.info(f"File 1 shape: {self.df1.shape}")
            logger.info(f"File 1 columns: {list(self.df1.columns)}")

            # Load second file
            logger.info(f"Loading: {self.file2_path}")
            self.df2 = pd.read_excel(self.file2_path)
            logger.info(f"File 2 shape: {self.df2.shape}")
            logger.info(f"File 2 columns: {list(self.df2.columns)}")

            return self.df1, self.df2

        except Exception as e:
            logger.error(f"Error loading Excel files: {e}")
            raise

    def clean_and_standardize_data(self) -> None:
        """Clean and standardize data from both files."""
        logger.info("Cleaning and standardizing data...")

        if self.df1 is None or self.df2 is None:
            self.load_excel_files()

        # Remove Hilfsspalte column from the first zulassung file (df2)
        if self.df2 is not None:
            # Check for Hilfsspalte column (case insensitive)
            hilfsspalte_cols = [
                col for col in self.df2.columns if "hilfsspalte" in col.lower()
            ]
            if hilfsspalte_cols:
                logger.info(
                    f"Removing Hilfsspalte columns from first zulassung file: {hilfsspalte_cols}"
                )
                self.df2 = self.df2.drop(columns=hilfsspalte_cols)
                logger.info(
                    f"File 2 shape after removing Hilfsspalte: {self.df2.shape}"
                )

        # Clean both dataframes
        for df, name in [(self.df1, "battery-id"), (self.df2, "first-zulassung")]:
            logger.info(f"Cleaning {name}...")
            initial_rows = len(df)

            # Standardize column names (remove extra spaces, convert to lowercase)
            df.columns = df.columns.str.strip().str.lower()

            # Clean AKZ and VIN fields
            if "akz" in df.columns:
                df["akz"] = df["akz"].astype(str).str.strip().str.upper()
                df["akz"] = df["akz"].replace(["nan", "", " ", "None"], None)

            if "vin" in df.columns:
                df["vin"] = df["vin"].astype(str).str.strip().str.upper()
                df["vin"] = df["vin"].replace(["nan", "", " ", "None"], None)

            # Clean Erstzulassung dates
            date_columns = [
                col
                for col in df.columns
                if "zulassung" in col.lower() or "erstzulassung" in col.lower()
            ]
            for date_col in date_columns:
                df[date_col] = pd.to_datetime(df[date_col], errors="coerce")
                logger.info(f"Found date column: {date_col}")

            # Clean battery type columns
            battery_columns = [
                col
                for col in df.columns
                if "batterie" in col.lower() or "battery" in col.lower()
            ]
            for battery_col in battery_columns:
                if df[battery_col].dtype == "object":
                    df[battery_col] = df[battery_col].astype(str).str.strip()
                    df[battery_col] = df[battery_col].replace(
                        ["nan", "", " ", "None"], None
                    )
                logger.info(f"Found battery column: {battery_col}")

            # Remove rows with missing key identifiers
            before_filter = len(df)
            df.dropna(
                subset=[col for col in ["akz", "vin"] if col in df.columns],
                how="all",
                inplace=True,
            )
            after_filter = len(df)

            if before_filter != after_filter:
                logger.warning(
                    f"{name}: Removed {before_filter - after_filter} rows with missing AKZ/VIN"
                )

            logger.info(f"{name}: {initial_rows} -> {len(df)} rows after cleaning")

    def find_matching_vehicles(self) -> pd.DataFrame:
        """Find vehicles that appear in both files based on AKZ and VIN."""
        logger.info("Finding matching vehicles between files...")

        if self.df1 is None or self.df2 is None:
            self.clean_and_standardize_data()

        # Create matching keys
        df1_keys = set()
        df2_keys = set()

        # Build keys from available columns
        key_columns = []
        if "akz" in self.df1.columns and "akz" in self.df2.columns:
            key_columns.append("akz")
        if "vin" in self.df1.columns and "vin" in self.df2.columns:
            key_columns.append("vin")

        if not key_columns:
            logger.error("No common key columns (AKZ, VIN) found in both files!")
            return pd.DataFrame()

        logger.info(f"Using matching columns: {key_columns}")

        # Create composite keys for matching
        self.df1["match_key"] = self.df1[key_columns].fillna("").agg("|".join, axis=1)
        self.df2["match_key"] = self.df2[key_columns].fillna("").agg("|".join, axis=1)

        # Remove empty keys
        self.df1 = self.df1[self.df1["match_key"] != "|"]
        self.df2 = self.df2[self.df2["match_key"] != "|"]

        # Find common keys
        df1_keys = set(self.df1["match_key"])
        df2_keys = set(self.df2["match_key"])
        common_keys = df1_keys.intersection(df2_keys)

        logger.info(f"File 1 unique vehicles: {len(df1_keys)}")
        logger.info(f"File 2 unique vehicles: {len(df2_keys)}")
        logger.info(f"Common vehicles: {len(common_keys)}")

        # Create merged dataset for common vehicles
        df1_common = self.df1[self.df1["match_key"].isin(common_keys)].copy()
        df2_common = self.df2[self.df2["match_key"].isin(common_keys)].copy()

        # Merge on match_key
        merged = pd.merge(
            df1_common,
            df2_common,
            on="match_key",
            suffixes=("_file1", "_file2"),
            how="inner",
        )

        self.common_vehicles = merged
        logger.info(f"Merged dataset shape: {merged.shape}")

        return merged

    def find_unique_vehicles(self) -> pd.DataFrame:
        """Find vehicles that exist in only one file but not the other."""
        logger.info("Finding vehicles unique to each file...")

        if self.df1 is None or self.df2 is None:
            self.clean_and_standardize_data()

        # Build keys from available columns
        key_columns = []
        if "akz" in self.df1.columns and "akz" in self.df2.columns:
            key_columns.append("akz")
        if "vin" in self.df1.columns and "vin" in self.df2.columns:
            key_columns.append("vin")

        if not key_columns:
            logger.error("No common key columns (AKZ, VIN) found in both files!")
            return pd.DataFrame()

        # Create composite keys for matching
        if "match_key" not in self.df1.columns:
            self.df1["match_key"] = (
                self.df1[key_columns].fillna("").agg("|".join, axis=1)
            )
        if "match_key" not in self.df2.columns:
            self.df2["match_key"] = (
                self.df2[key_columns].fillna("").agg("|".join, axis=1)
            )

        # Remove empty keys
        df1_clean = self.df1[self.df1["match_key"] != "|"].copy()
        df2_clean = self.df2[self.df2["match_key"] != "|"].copy()

        # Get unique keys from each file
        df1_keys = set(df1_clean["match_key"])
        df2_keys = set(df2_clean["match_key"])

        # Find vehicles unique to each file
        unique_to_file1 = df1_keys - df2_keys
        unique_to_file2 = df2_keys - df1_keys

        logger.info(f"Vehicles unique to battery-id file: {len(unique_to_file1)}")
        logger.info(f"Vehicles unique to first-zulassung file: {len(unique_to_file2)}")

        # Create datasets for unique vehicles
        unique_vehicles_list = []

        # Add vehicles unique to battery-id file
        file1_unique = df1_clean[df1_clean["match_key"].isin(unique_to_file1)].copy()
        file1_unique["source_file"] = "battery-id"
        file1_unique["file_source"] = "Fahrzeug Batterie Zuordnung-with-batteryid.xlsx"
        unique_vehicles_list.append(file1_unique)

        # Add vehicles unique to first-zulassung file
        file2_unique = df2_clean[df2_clean["match_key"].isin(unique_to_file2)].copy()
        file2_unique["source_file"] = "first-zulassung"
        file2_unique["file_source"] = (
            "Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx"
        )
        unique_vehicles_list.append(file2_unique)

        # Combine all unique vehicles
        if unique_vehicles_list:
            # Align columns - get all columns from both dataframes
            all_columns = set()
            for df in unique_vehicles_list:
                all_columns.update(df.columns)

            # Ensure all dataframes have the same columns
            for df in unique_vehicles_list:
                for col in all_columns:
                    if col not in df.columns:
                        df[col] = None

            # Reorder columns consistently
            column_order = ["source_file", "file_source", "match_key"] + [
                col
                for col in sorted(all_columns)
                if col not in ["source_file", "file_source", "match_key"]
            ]

            for i, df in enumerate(unique_vehicles_list):
                unique_vehicles_list[i] = df[column_order]

            unique_vehicles = pd.concat(unique_vehicles_list, ignore_index=True)
        else:
            unique_vehicles = pd.DataFrame()

        logger.info(f"Total unique vehicles combined: {len(unique_vehicles)}")

        # Store results
        self.unique_vehicles = unique_vehicles

        # Update summary stats
        if not hasattr(self, "summary_stats"):
            self.summary_stats = {}

        self.summary_stats["unique_vehicles"] = {
            "total_unique_vehicles": len(unique_vehicles),
            "unique_to_battery_id_file": len(file1_unique),
            "unique_to_first_zulassung_file": len(file2_unique),
            "unique_to_file1_keys": (
                list(unique_to_file1)[:10]
                if len(unique_to_file1) <= 10
                else list(unique_to_file1)[:10] + ["..."]
            ),
            "unique_to_file2_keys": (
                list(unique_to_file2)[:10]
                if len(unique_to_file2) <= 10
                else list(unique_to_file2)[:10] + ["..."]
            ),
        }

        return unique_vehicles

    def compare_erstzulassung_dates(self) -> pd.DataFrame:
        """Compare Erstzulassung dates between the two files."""
        logger.info("Comparing Erstzulassung dates...")

        if self.common_vehicles is None:
            self.find_matching_vehicles()

        if self.common_vehicles.empty:
            logger.warning("No common vehicles found for date comparison")
            return pd.DataFrame()

        # Find date columns in both files
        date_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "zulassung" in col.lower() and "file1" in col
        ]
        date_cols_file2 = [
            col
            for col in self.common_vehicles.columns
            if "zulassung" in col.lower() and "file2" in col
        ]

        logger.info(f"Date columns battery-id file: {date_cols_file1}")
        logger.info(f"Date columns first-zulassung file: {date_cols_file2}")

        if not date_cols_file1 or not date_cols_file2:
            logger.warning("No Erstzulassung date columns found in one or both files")
            return pd.DataFrame()

        # Compare dates
        comparison_results = []

        for _, row in self.common_vehicles.iterrows():
            for date_col1 in date_cols_file1:
                for date_col2 in date_cols_file2:
                    date1 = row[date_col1]  # battery-id file date
                    date2 = row[date_col2]  # first-zulassung file date

                    # Initialize comparison variables
                    dates_match = False
                    date_swapped = None
                    note = ""

                    if pd.notna(date1) and pd.notna(date2):
                        # First check direct match
                        if date1 == date2:
                            dates_match = True
                            note = "Direct match"
                        else:
                            # Try swapping month and day in first-zulassung date
                            try:
                                # Extract year, month, day from first-zulassung date
                                year = date2.year
                                month = date2.month
                                day = date2.day

                                # Only swap if month and day are different and both are valid
                                if month != day and month <= 12 and day <= 12:
                                    date_swapped = pd.Timestamp(
                                        year=year, month=day, day=month
                                    )

                                    if date1 == date_swapped:
                                        dates_match = True
                                        note = "Match after swapping month/day in first-zulassung"
                                    else:
                                        note = f"No match even after swap (original: {date2.strftime('%Y-%m-%d')}, swapped: {date_swapped.strftime('%Y-%m-%d')})"
                                else:
                                    note = f"Cannot swap month/day (month={month}, day={day})"
                            except (ValueError, AttributeError) as e:
                                note = f"Error swapping date: {e}"

                        if not dates_match and not note:
                            note = "Dates don't match"

                    result = {
                        "match_key": row["match_key"],
                        "akz_battery_id": row.get("akz_file1", ""),
                        "vin_battery_id": row.get("vin_file1", ""),
                        "akz_first_zulassung": row.get("akz_file2", ""),
                        "vin_first_zulassung": row.get("vin_file2", ""),
                        "date_col_battery_id": date_col1,
                        "date_col_first_zulassung": date_col2,
                        "date_battery_id": date1,
                        "date_first_zulassung": date2,
                        "date_first_zulassung_swapped": date_swapped,
                        "dates_match": dates_match,
                        "date_difference_days": (
                            (date2 - date1).days
                            if pd.notna(date1) and pd.notna(date2)
                            else None
                        ),
                        "both_dates_available": pd.notna(date1) and pd.notna(date2),
                        "battery_id_date_missing": pd.isna(date1),
                        "first_zulassung_date_missing": pd.isna(date2),
                        "note": note,
                    }
                    comparison_results.append(result)

        self.date_comparison = pd.DataFrame(comparison_results)

        # Summary statistics
        if not self.date_comparison.empty:
            total_comparisons = len(self.date_comparison)
            matching_dates = self.date_comparison["dates_match"].sum()
            both_available = self.date_comparison["both_dates_available"].sum()

            logger.info(f"Date comparison results:")
            logger.info(f"  Total comparisons: {total_comparisons}")
            logger.info(f"  Both dates available: {both_available}")
            logger.info(f"  Matching dates: {matching_dates}")
            logger.info(
                f"  Match rate: {matching_dates/both_available*100:.1f}%"
                if both_available > 0
                else "N/A"
            )

        return self.date_comparison

    def compare_battery_ids(self) -> pd.DataFrame:
        """Compare HV Master and Slave battery IDs between the two files for same vehicles."""
        logger.info("Comparing HV Master and Slave battery IDs...")

        if self.common_vehicles is None:
            self.find_matching_vehicles()

        if self.common_vehicles.empty:
            logger.warning("No common vehicles found for battery comparison")
            return pd.DataFrame()

        # Find HV Master and Slave columns in both files
        master_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "master" in col.lower() and "file1" in col and "hv" in col.lower()
        ]
        slave_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "slave" in col.lower() and "file1" in col and "hv" in col.lower()
        ]
        master_cols_file2 = [
            col
            for col in self.common_vehicles.columns
            if "master" in col.lower() and "file2" in col and "hv" in col.lower()
        ]
        slave_cols_file2 = [
            col
            for col in self.common_vehicles.columns
            if "slave" in col.lower() and "file2" in col and "hv" in col.lower()
        ]

        logger.info(f"Battery-ID file - Master columns: {master_cols_file1}")
        logger.info(f"Battery-ID file - Slave columns: {slave_cols_file1}")
        logger.info(f"First-Zulassung file - Master columns: {master_cols_file2}")
        logger.info(f"First-Zulassung file - Slave columns: {slave_cols_file2}")

        battery_results = []

        # Find Erstzulassung date columns in battery-id file
        erstzulassung_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "zulassung" in col.lower() and "file1" in col
        ]

        logger.info(
            f"Erstzulassung columns in battery-id file: {erstzulassung_cols_file1}"
        )

        for _, row in self.common_vehicles.iterrows():
            vin = row.get("vin_file1", row.get("vin_file2", ""))
            akz = row.get("akz_file1", row.get("akz_file2", ""))

            # Get Erstzulassung date from battery-id file
            erstzulassung_date = None
            if erstzulassung_cols_file1:
                erstzulassung_date = row[erstzulassung_cols_file1[0]]
                if pd.isna(erstzulassung_date):
                    erstzulassung_date = None

            # Get battery IDs from both files
            master_battery_id = row[master_cols_file1[0]] if master_cols_file1 else None
            slave_battery_id = row[slave_cols_file1[0]] if slave_cols_file1 else None
            master_first_zulassung = (
                row[master_cols_file2[0]] if master_cols_file2 else None
            )
            slave_first_zulassung = (
                row[slave_cols_file2[0]] if slave_cols_file2 else None
            )

            # Clean battery IDs (remove NaN, empty strings, etc.)
            def clean_battery_id(battery_id):
                if pd.isna(battery_id) or str(battery_id).strip() in [
                    "",
                    "nan",
                    "None",
                ]:
                    return None
                return str(battery_id).strip()

            master_battery_id = clean_battery_id(master_battery_id)
            slave_battery_id = clean_battery_id(slave_battery_id)
            master_first_zulassung = clean_battery_id(master_first_zulassung)
            slave_first_zulassung = clean_battery_id(slave_first_zulassung)

            # Compare Master batteries
            master_match = master_battery_id == master_first_zulassung
            master_both_available = (
                master_battery_id is not None and master_first_zulassung is not None
            )

            # Compare Slave batteries
            slave_match = slave_battery_id == slave_first_zulassung
            slave_both_available = (
                slave_battery_id is not None and slave_first_zulassung is not None
            )

            # Overall assessment
            all_match = master_match and slave_match
            any_mismatch = (master_both_available and not master_match) or (
                slave_both_available and not slave_match
            )

            # Create mismatch description
            issues = []
            if master_both_available and not master_match:
                issues.append(
                    f"Master: '{master_battery_id}' vs '{master_first_zulassung}'"
                )
            if slave_both_available and not slave_match:
                issues.append(
                    f"Slave: '{slave_battery_id}' vs '{slave_first_zulassung}'"
                )

            if not issues:
                if master_both_available and slave_both_available:
                    mismatch_description = "All batteries match"
                else:
                    mismatch_description = ""  # Leave blank for missing data
            else:
                mismatch_description = "; ".join(issues)

            result = {
                "vin": vin,
                "akz": akz,
                "erstzulassung": erstzulassung_date,
                "master_battery_id_file": master_battery_id or "",
                "master_first_zulassung_file": master_first_zulassung or "",
                "slave_battery_id_file": slave_battery_id or "",
                "slave_first_zulassung_file": slave_first_zulassung or "",
                "master_batteries_match": master_match,
                "slave_batteries_match": slave_match,
                "all_batteries_match": all_match,
                "has_mismatch": any_mismatch,
                "mismatch_description": mismatch_description,
                "master_both_available": master_both_available,
                "slave_both_available": slave_both_available,
            }
            battery_results.append(result)

        self.battery_comparison = pd.DataFrame(battery_results)

        # Summary statistics
        if not self.battery_comparison.empty:
            total_vehicles = len(self.battery_comparison)
            vehicles_with_mismatches = self.battery_comparison["has_mismatch"].sum()
            master_mismatches = (
                self.battery_comparison["master_both_available"]
                & ~self.battery_comparison["master_batteries_match"]
            ).sum()
            slave_mismatches = (
                self.battery_comparison["slave_both_available"]
                & ~self.battery_comparison["slave_batteries_match"]
            ).sum()

            logger.info(f"Battery ID comparison results:")
            logger.info(f"  Total vehicles: {total_vehicles}")
            logger.info(
                f"  Vehicles with battery mismatches: {vehicles_with_mismatches}"
            )
            logger.info(f"  Master battery mismatches: {master_mismatches}")
            logger.info(f"  Slave battery mismatches: {slave_mismatches}")
            logger.info(
                f"  Mismatch rate: {vehicles_with_mismatches/total_vehicles*100:.1f}%"
            )

            if vehicles_with_mismatches > 0:
                logger.info("  Sample mismatches:")
                mismatched_vehicles = self.battery_comparison[
                    self.battery_comparison["has_mismatch"] == True
                ]
                for _, mismatch in mismatched_vehicles.head(5).iterrows():
                    logger.info(
                        f"    VIN {mismatch['vin']}: {mismatch['mismatch_description']}"
                    )

        return self.battery_comparison

    def _classify_battery_type(self, battery_info: str) -> str:
        """Classify battery type - only interested in HV Batterie (Bei E18 Master) and HV Batterie (Bei E18 Slave)."""
        if pd.isna(battery_info) or battery_info == "None":
            return "Unknown"

        battery_str = str(battery_info).strip()

        # Look for exact matches or close matches to the specific battery types we care about
        if "HV Batterie (Bei E18 Master)" in battery_str:
            return "HV Batterie (Bei E18 Master)"
        elif "HV Batterie (Bei E18 Slave)" in battery_str:
            return "HV Batterie (Bei E18 Slave)"
        elif "e18" in battery_str.lower() and "master" in battery_str.lower():
            return "HV Batterie (Bei E18 Master)"
        elif "e18" in battery_str.lower() and "slave" in battery_str.lower():
            return "HV Batterie (Bei E18 Slave)"
        else:
            return "Other/Unknown"

    def generate_summary_statistics(self) -> Dict:
        """Generate comprehensive summary statistics."""
        logger.info("Generating summary statistics...")

        stats = {
            "data_overview": {
                "file1_total_records": len(self.df1) if self.df1 is not None else 0,
                "file2_total_records": len(self.df2) if self.df2 is not None else 0,
                "common_vehicles": (
                    len(self.common_vehicles) if self.common_vehicles is not None else 0
                ),
            },
            "date_analysis": {},
            "battery_analysis": {},
        }

        # Date analysis summary
        if self.date_comparison is not None and not self.date_comparison.empty:
            date_stats = {
                "total_date_comparisons": len(self.date_comparison),
                "both_dates_available": self.date_comparison[
                    "both_dates_available"
                ].sum(),
                "matching_dates": self.date_comparison["dates_match"].sum(),
                "match_rate_percent": (
                    self.date_comparison["dates_match"].sum()
                    / self.date_comparison["both_dates_available"].sum()
                    * 100
                    if self.date_comparison["both_dates_available"].sum() > 0
                    else 0
                ),
                "avg_date_difference_days": (
                    self.date_comparison["date_difference_days"].mean()
                    if "date_difference_days" in self.date_comparison.columns
                    else None
                ),
            }
            stats["date_analysis"] = date_stats

        # Battery analysis summary
        if self.battery_comparison is not None and not self.battery_comparison.empty:
            battery_stats = {
                "total_vehicles": len(self.battery_comparison),
                "vehicles_with_mismatches": self.battery_comparison[
                    "has_mismatch"
                ].sum(),
                "master_battery_mismatches": (
                    self.battery_comparison["master_both_available"]
                    & ~self.battery_comparison["master_batteries_match"]
                ).sum(),
                "slave_battery_mismatches": (
                    self.battery_comparison["slave_both_available"]
                    & ~self.battery_comparison["slave_batteries_match"]
                ).sum(),
                "mismatch_rate_percent": (
                    self.battery_comparison["has_mismatch"].sum()
                    / len(self.battery_comparison)
                    * 100
                ),
                "master_both_available": self.battery_comparison[
                    "master_both_available"
                ].sum(),
                "slave_both_available": self.battery_comparison[
                    "slave_both_available"
                ].sum(),
            }
            stats["battery_analysis"] = battery_stats

        self.summary_stats = stats
        return stats

    def export_results(self, output_dir: str = "comparison_results") -> Dict[str, str]:
        """Export all comparison results to CSV files."""
        logger.info(f"Exporting results to {output_dir}/...")

        # Create output directory
        Path(output_dir).mkdir(exist_ok=True)

        exported_files = {}

        # Export common vehicles
        if self.common_vehicles is not None and not self.common_vehicles.empty:
            common_path = f"{output_dir}/common_vehicles.csv"
            self.common_vehicles.to_csv(common_path, index=False)
            exported_files["common_vehicles"] = common_path
            logger.info(f"Exported common vehicles: {common_path}")

        # Export unique vehicles
        if (
            hasattr(self, "unique_vehicles")
            and self.unique_vehicles is not None
            and not self.unique_vehicles.empty
        ):
            unique_path = f"{output_dir}/unique_vehicles.csv"
            self.unique_vehicles.to_csv(unique_path, index=False)
            exported_files["unique_vehicles"] = unique_path
            logger.info(f"Exported unique vehicles: {unique_path}")

        # Export date comparison
        if self.date_comparison is not None and not self.date_comparison.empty:
            date_path = f"{output_dir}/date_comparison.csv"
            self.date_comparison.to_csv(date_path, index=False)
            exported_files["date_comparison"] = date_path
            logger.info(f"Exported date comparison: {date_path}")

        # Export battery comparison
        if self.battery_comparison is not None and not self.battery_comparison.empty:
            battery_path = f"{output_dir}/battery_comparison.csv"
            self.battery_comparison.to_csv(battery_path, index=False)
            exported_files["battery_comparison"] = battery_path
            logger.info(f"Exported battery comparison: {battery_path}")

            # Export battery mismatches only
        mismatched_batteries = self.battery_comparison[
            self.battery_comparison["has_mismatch"] == True
        ]
        if not mismatched_batteries.empty:
            mismatch_path = f"{output_dir}/battery_mismatches.csv"
            mismatched_batteries.to_csv(mismatch_path, index=False)
            exported_files["battery_mismatches"] = mismatch_path
            logger.info(
                f"Exported battery mismatches: {mismatch_path} ({len(mismatched_batteries)} vehicles)"
            )

        # Export working CSV files
        working_matching = self.generate_working_csv_matching_vehicles()
        if not working_matching.empty:
            working_matching_path = f"{output_dir}/working_matching_vehicles.csv"
            working_matching.to_csv(working_matching_path, index=False)
            exported_files["working_matching_vehicles"] = working_matching_path
            logger.info(
                f"Exported working matching vehicles CSV: {working_matching_path}"
            )

        working_unique = self.generate_working_csv_unique_vehicles()
        if not working_unique.empty:
            working_unique_path = f"{output_dir}/working_unique_vehicles.csv"
            working_unique.to_csv(working_unique_path, index=False)
            exported_files["working_unique_vehicles"] = working_unique_path
            logger.info(f"Exported working unique vehicles CSV: {working_unique_path}")

        working_mismatches = self.generate_working_csv_mismatches()
        if not working_mismatches.empty:
            working_mismatches_path = f"{output_dir}/working_mismatches.csv"
            working_mismatches.to_csv(working_mismatches_path, index=False)
            exported_files["working_mismatches"] = working_mismatches_path
            logger.info(f"Exported working mismatches CSV: {working_mismatches_path}")

        # Export summary statistics
        if self.summary_stats:
            stats_path = f"{output_dir}/summary_statistics.txt"
            with open(stats_path, "w") as f:
                f.write("Vehicle-Battery Comparison Summary\n")
                f.write("=" * 40 + "\n\n")

                for category, stats in self.summary_stats.items():
                    f.write(f"{category.upper()}\n")
                    f.write("-" * 20 + "\n")
                    for key, value in stats.items():
                        f.write(f"{key}: {value}\n")
                    f.write("\n")

            exported_files["summary_statistics"] = stats_path
            logger.info(f"Exported summary statistics: {stats_path}")

        return exported_files

    def run_complete_analysis(self) -> Dict:
        """Run the complete comparison analysis."""
        logger.info("Starting complete vehicle-battery comparison analysis...")

        try:
            # Step 1: Load and clean data
            self.load_excel_files()
            self.clean_and_standardize_data()

            # Step 2: Find matching vehicles
            self.find_matching_vehicles()

            # Step 3: Find unique vehicles
            self.find_unique_vehicles()

            # Step 4: Compare dates
            self.compare_erstzulassung_dates()

            # Step 5: Compare battery IDs
            self.compare_battery_ids()

            # Step 6: Generate statistics
            self.generate_summary_statistics()

            # Step 7: Export results
            exported_files = self.export_results()

            logger.info("Analysis completed successfully!")

            return {
                "success": True,
                "summary_stats": self.summary_stats,
                "exported_files": exported_files,
                "common_vehicles_count": (
                    len(self.common_vehicles) if self.common_vehicles is not None else 0
                ),
                "unique_vehicles_count": (
                    len(self.unique_vehicles)
                    if hasattr(self, "unique_vehicles")
                    and self.unique_vehicles is not None
                    else 0
                ),
            }

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "summary_stats": self.summary_stats,
                "exported_files": {},
            }

    def run_unique_vehicles_analysis(self) -> Dict:
        """Run analysis to find and export only unique vehicles."""
        logger.info("Starting unique vehicles analysis...")

        try:
            # Step 1: Load and clean data
            self.load_excel_files()
            self.clean_and_standardize_data()

            # Step 2: Find unique vehicles
            self.find_unique_vehicles()

            # Step 3: Export results
            exported_files = self.export_results()

            logger.info("Unique vehicles analysis completed successfully!")

            return {
                "success": True,
                "summary_stats": self.summary_stats,
                "exported_files": exported_files,
                "unique_vehicles_count": (
                    len(self.unique_vehicles)
                    if hasattr(self, "unique_vehicles")
                    and self.unique_vehicles is not None
                    else 0
                ),
            }

        except Exception as e:
            logger.error(f"Unique vehicles analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "summary_stats": self.summary_stats,
                "exported_files": {},
            }

    def load_hv_repair_data(
        self, repair_file_path: str = "hv_repair_2025-06-02b.csv"
    ) -> pd.DataFrame:
        """Load and process HV repair data to identify battery changes and latest battery IDs."""
        logger.info(f"Loading HV repair data from {repair_file_path}...")

        try:
            # Check if file exists
            if not Path(repair_file_path).exists():
                logger.warning(f"HV repair file not found: {repair_file_path}")
                self.hv_repair_data = pd.DataFrame()
                return pd.DataFrame()

            # Load repair data
            repair_df = pd.read_csv(repair_file_path)
            logger.info(f"Loaded HV repair data: {repair_df.shape}")

            # Clean and standardize VIN
            repair_df["vin"] = repair_df["vin"].astype(str).str.strip().str.upper()
            repair_df = repair_df[repair_df["vin"] != "NAN"]

            # Convert dates
            repair_df["created"] = pd.to_datetime(repair_df["created"], errors="coerce")
            repair_df["battery_changed"] = pd.to_datetime(
                repair_df["battery_changed"], errors="coerce"
            )

            # Clean battery IDs
            def clean_battery_id(battery_id):
                if pd.isna(battery_id) or str(battery_id).strip() in [
                    "",
                    "nan",
                    "None",
                    "--",
                ]:
                    return None
                return str(battery_id).strip()

            repair_df["battery_id_old"] = repair_df["battery_id_old"].apply(
                clean_battery_id
            )
            repair_df["battery_id_new"] = repair_df["battery_id_new"].apply(
                clean_battery_id
            )

            # Sort by VIN and created date to get chronological order
            repair_df = repair_df.sort_values(["vin", "created"]).reset_index(drop=True)

            logger.info(
                f"Processed HV repair data: {len(repair_df)} records for {repair_df['vin'].nunique()} unique vehicles"
            )

            self.hv_repair_data = repair_df
            return repair_df

        except Exception as e:
            logger.error(f"Error loading HV repair data: {e}")
            self.hv_repair_data = pd.DataFrame()
            return pd.DataFrame()

    def get_latest_battery_ids_for_vin(self, vin: str) -> Dict[str, Optional[str]]:
        """Get the latest Master and Slave battery IDs for a given VIN from repair history."""
        if not hasattr(self, "hv_repair_data") or self.hv_repair_data.empty:
            return {"latest_master": None, "latest_slave": None, "repair_chain": []}

        # Get all repair records for this VIN
        vin_repairs = self.hv_repair_data[
            self.hv_repair_data["vin"] == vin.upper()
        ].copy()

        if vin_repairs.empty:
            return {"latest_master": None, "latest_slave": None, "repair_chain": []}

        # Sort by created date to get chronological order
        vin_repairs = vin_repairs.sort_values("created")

        # Build battery change chain
        repair_chain = []
        latest_batteries = {"master": None, "slave": None}

        for _, repair in vin_repairs.iterrows():
            old_id = repair["battery_id_old"]
            new_id = repair["battery_id_new"]
            action = repair["action"]
            battery_changed_date = repair["battery_changed"]
            created_date = repair["created"]

            repair_info = {
                "action": action,
                "created": created_date,
                "battery_changed": battery_changed_date,
                "old_id": old_id,
                "new_id": new_id,
            }
            repair_chain.append(repair_info)

            # Track the latest battery ID - use new_id if available, otherwise old_id
            if new_id:
                # Determine if this is master or slave based on battery type or ID pattern
                # For now, we'll track all batteries together and sort them out later
                if old_id and new_id != old_id:
                    # This is a battery change
                    latest_batteries["master"] = new_id  # Simplified assumption
                elif not old_id and new_id:
                    # This is a new battery installation
                    latest_batteries["master"] = new_id
            elif old_id and not new_id:
                # This might be a removal or repair without replacement
                pass

        return {
            "latest_master": latest_batteries["master"],
            "latest_slave": latest_batteries["slave"],
            "repair_chain": repair_chain,
        }

    def check_battery_mismatch_against_repairs(
        self,
        vin: str,
        master_file1: str,
        slave_file1: str,
        master_file2: str,
        slave_file2: str,
    ) -> Dict:
        """Check if battery mismatches can be explained by HV repairs."""

        # Get repair history for this VIN
        repair_info = self.get_latest_battery_ids_for_vin(vin)

        if not repair_info["repair_chain"]:
            # No repair history found
            return {
                "is_repair_related": False,
                "resolution": "true_mismatch",
                "latest_master": None,
                "latest_slave": None,
                "explanation": "No repair history found for this VIN",
                "repair_chain": [],
            }

        # Get all battery IDs mentioned in repair history
        repair_battery_ids = set()
        for repair in repair_info["repair_chain"]:
            if repair["old_id"]:
                repair_battery_ids.add(repair["old_id"])
            if repair["new_id"]:
                repair_battery_ids.add(repair["new_id"])

        # Clean input battery IDs
        def clean_id(bid):
            return (
                str(bid).strip()
                if bid and str(bid).strip() not in ["", "nan", "None"]
                else None
            )

        master_file1 = clean_id(master_file1)
        slave_file1 = clean_id(slave_file1)
        master_file2 = clean_id(master_file2)
        slave_file2 = clean_id(slave_file2)

        file1_batteries = {master_file1, slave_file1} - {None}
        file2_batteries = {master_file2, slave_file2} - {None}
        all_file_batteries = file1_batteries.union(file2_batteries)

        # Check if any of the mismatched batteries appear in repair history
        batteries_in_repair = all_file_batteries.intersection(repair_battery_ids)

        if batteries_in_repair:
            # Find the latest battery change date to determine which snapshot is more recent
            latest_change_date = None
            latest_battery_id = None

            for repair in repair_info["repair_chain"]:
                if repair["battery_changed"] and pd.notna(repair["battery_changed"]):
                    if (
                        not latest_change_date
                        or repair["battery_changed"] > latest_change_date
                    ):
                        latest_change_date = repair["battery_changed"]
                        if repair["new_id"]:
                            latest_battery_id = repair["new_id"]

            # Find which file likely has the latest battery
            explanation_parts = []

            # Check if file1 has batteries that appear in repairs
            file1_in_repair = file1_batteries.intersection(repair_battery_ids)
            file2_in_repair = file2_batteries.intersection(repair_battery_ids)

            if file1_in_repair and file2_in_repair:
                explanation_parts.append(
                    f"Both files have batteries found in repair history"
                )
                explanation_parts.append(
                    f"File1 batteries in repairs: {file1_in_repair}"
                )
                explanation_parts.append(
                    f"File2 batteries in repairs: {file2_in_repair}"
                )
            elif file1_in_repair:
                explanation_parts.append(
                    f"File1 has batteries in repair history: {file1_in_repair}"
                )
            elif file2_in_repair:
                explanation_parts.append(
                    f"File2 has batteries in repair history: {file2_in_repair}"
                )

            if latest_change_date:
                explanation_parts.append(
                    f"Latest battery change: {latest_change_date.strftime('%Y-%m-%d')}"
                )

            if latest_battery_id:
                explanation_parts.append(f"Latest battery ID: {latest_battery_id}")

            return {
                "is_repair_related": True,
                "resolution": "recoverable_via_repair",
                "latest_master": latest_battery_id,  # Simplified - in reality would need to distinguish master/slave
                "latest_slave": None,  # Would need more sophisticated logic to determine master vs slave
                "explanation": "; ".join(explanation_parts),
                "repair_chain": repair_info["repair_chain"],
                "latest_change_date": latest_change_date,
            }
        else:
            return {
                "is_repair_related": False,
                "resolution": "true_mismatch",
                "latest_master": None,
                "latest_slave": None,
                "explanation": f"Battery IDs not found in repair history. File batteries: {all_file_batteries}, Repair batteries: {repair_battery_ids}",
                "repair_chain": repair_info["repair_chain"],
            }

    def generate_working_csv_matching_vehicles(self) -> pd.DataFrame:
        """Generate a working CSV with matching vehicles in the requested format, including recovered vehicles."""
        logger.info("Generating working CSV for matching vehicles...")

        if self.common_vehicles is None:
            self.find_matching_vehicles()

        if self.date_comparison is None:
            self.compare_erstzulassung_dates()

        if self.battery_comparison is None:
            self.compare_battery_ids()

        if self.common_vehicles.empty:
            logger.warning("No common vehicles found for working CSV")
            return pd.DataFrame()

        # Find the relevant columns for batteries and dates
        master_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "master" in col.lower() and "file1" in col and "hv" in col.lower()
        ]
        slave_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "slave" in col.lower() and "file1" in col and "hv" in col.lower()
        ]
        erstzulassung_cols_file1 = [
            col
            for col in self.common_vehicles.columns
            if "zulassung" in col.lower() and "file1" in col
        ]

        # We need to generate mismatches first to know which vehicles to exclude
        # Call generate_working_csv_mismatches to ensure we have the true mismatches identified
        if not hasattr(self, "recovered_vehicles"):
            # This will populate self.recovered_vehicles and identify true mismatches
            _ = self.generate_working_csv_mismatches()

        # Create the working dataset
        working_data = []

        for _, row in self.common_vehicles.iterrows():
            # Get basic vehicle info
            akz = row.get("akz_file1", row.get("akz_file2", ""))
            vin = row.get("vin_file1", row.get("vin_file2", ""))

            # Get battery IDs (preferring file1 - battery-id file)
            master = row[master_cols_file1[0]] if master_cols_file1 else ""
            slave = row[slave_cols_file1[0]] if slave_cols_file1 else ""

            # Get erstzulassung date (preferring file1 - battery-id file)
            erstzulassung = (
                row[erstzulassung_cols_file1[0]] if erstzulassung_cols_file1 else None
            )

            # Clean battery IDs
            if pd.isna(master) or str(master).strip() in ["", "nan", "None"]:
                master = ""
            else:
                master = str(master).strip()

            if pd.isna(slave) or str(slave).strip() in ["", "nan", "None"]:
                slave = ""
            else:
                slave = str(slave).strip()

            # Find matching date comparison record to get the note
            match_key = row["match_key"]
            date_note = ""

            if self.date_comparison is not None and not self.date_comparison.empty:
                matching_date_records = self.date_comparison[
                    self.date_comparison["match_key"] == match_key
                ]
                if not matching_date_records.empty:
                    # Get the first matching record's note
                    date_record = matching_date_records.iloc[0]
                    if date_record["dates_match"]:
                        date_note = date_record["note"]
                    else:
                        date_note = "Dates don't match"

            working_record = {
                "akz": akz,
                "vin": vin,
                "master": master,
                "slave": slave,
                "erstzulassung": erstzulassung,
                "note": date_note,
            }
            working_data.append(working_record)

        working_df = pd.DataFrame(working_data)

        # Add recovered vehicles from HV repair analysis
        if hasattr(self, "recovered_vehicles") and not self.recovered_vehicles.empty:
            logger.info(
                f"Adding {len(self.recovered_vehicles)} recovered vehicles from HV repair analysis"
            )
            working_df = pd.concat(
                [working_df, self.recovered_vehicles], ignore_index=True
            )

        # Remove duplicates based on all columns
        working_df = working_df.drop_duplicates()

        logger.info(
            f"Generated working CSV with {len(working_df)} matching vehicle records (including recovered vehicles)"
        )

        return working_df

    def generate_working_csv_unique_vehicles(self) -> pd.DataFrame:
        """Generate a working CSV with unique vehicles showing source file."""
        logger.info("Generating working CSV for unique vehicles...")

        if not hasattr(self, "unique_vehicles") or self.unique_vehicles is None:
            self.find_unique_vehicles()

        if self.unique_vehicles.empty:
            logger.warning("No unique vehicles found for working CSV")
            return pd.DataFrame()

        # Create the working dataset for unique vehicles
        working_unique_data = []

        for _, row in self.unique_vehicles.iterrows():
            # Get vehicle info
            akz = ""
            vin = ""
            master = ""
            slave = ""
            erstzulassung = None
            source_file = row.get("source_file", "")

            # Extract info based on which file this came from
            if source_file == "battery-id":
                # From battery-id file
                akz = row.get("akz", "")
                vin = row.get("vin", "")

                # Find master/slave battery columns
                master_cols = [
                    col
                    for col in row.index
                    if "master" in col.lower() and "hv" in col.lower()
                ]
                slave_cols = [
                    col
                    for col in row.index
                    if "slave" in col.lower() and "hv" in col.lower()
                ]
                erstzulassung_cols = [
                    col for col in row.index if "zulassung" in col.lower()
                ]

                if master_cols:
                    master = row[master_cols[0]]
                if slave_cols:
                    slave = row[slave_cols[0]]
                if erstzulassung_cols:
                    erstzulassung = row[erstzulassung_cols[0]]

            elif source_file == "first-zulassung":
                # From first-zulassung file
                akz = row.get("akz", "")
                vin = row.get("vin", "")

                # Find master/slave battery columns
                master_cols = [
                    col
                    for col in row.index
                    if "master" in col.lower() and "hv" in col.lower()
                ]
                slave_cols = [
                    col
                    for col in row.index
                    if "slave" in col.lower() and "hv" in col.lower()
                ]
                erstzulassung_cols = [
                    col for col in row.index if "zulassung" in col.lower()
                ]

                if master_cols:
                    master = row[master_cols[0]]
                if slave_cols:
                    slave = row[slave_cols[0]]
                if erstzulassung_cols:
                    erstzulassung = row[erstzulassung_cols[0]]

            # Clean battery IDs
            if pd.isna(master) or str(master).strip() in ["", "nan", "None"]:
                master = ""
            else:
                master = str(master).strip()

            if pd.isna(slave) or str(slave).strip() in ["", "nan", "None"]:
                slave = ""
            else:
                slave = str(slave).strip()

            # Clean other fields
            if pd.isna(akz) or str(akz).strip() in ["", "nan", "None"]:
                akz = ""
            else:
                akz = str(akz).strip()

            if pd.isna(vin) or str(vin).strip() in ["", "nan", "None"]:
                vin = ""
            else:
                vin = str(vin).strip()

            working_record = {
                "akz": akz,
                "vin": vin,
                "master": master,
                "slave": slave,
                "erstzulassung": erstzulassung,
                "note": f"Unique to {source_file} file",
            }
            working_unique_data.append(working_record)

        working_unique_df = pd.DataFrame(working_unique_data)

        # Remove duplicates
        working_unique_df = working_unique_df.drop_duplicates()

        logger.info(
            f"Generated working CSV with {len(working_unique_df)} unique vehicle records"
        )

        return working_unique_df

    def generate_working_csv_mismatches(self) -> pd.DataFrame:
        """Generate a working CSV with TRUE mismatches (after checking against HV repair data)."""
        logger.info(
            "Generating working CSV for TRUE mismatches after HV repair analysis..."
        )

        # Load HV repair data if not already loaded
        if not hasattr(self, "hv_repair_data"):
            self.load_hv_repair_data()

        if self.common_vehicles is None:
            self.find_matching_vehicles()

        if self.date_comparison is None:
            self.compare_erstzulassung_dates()

        if self.battery_comparison is None:
            self.compare_battery_ids()

        if self.common_vehicles.empty:
            logger.warning("No common vehicles found for mismatch CSV")
            return pd.DataFrame()

        true_mismatch_data = []
        recovered_vehicles_data = []  # Store vehicles recovered due to HV repairs

        # Get vehicles with date mismatches
        if self.date_comparison is not None and not self.date_comparison.empty:
            date_mismatches = self.date_comparison[~self.date_comparison["dates_match"]]

            for _, date_mismatch in date_mismatches.iterrows():
                match_key = date_mismatch["match_key"]

                # Find the corresponding vehicle in common_vehicles
                vehicle_records = self.common_vehicles[
                    self.common_vehicles["match_key"] == match_key
                ]
                if vehicle_records.empty:
                    continue

                vehicle_record = vehicle_records.iloc[0]

                # Get basic vehicle info
                akz = vehicle_record.get(
                    "akz_file1", vehicle_record.get("akz_file2", "")
                )
                vin = vehicle_record.get(
                    "vin_file1", vehicle_record.get("vin_file2", "")
                )

                # Get battery IDs from file1 (battery-id file)
                master_cols_file1 = [
                    col
                    for col in vehicle_record.index
                    if "master" in col.lower()
                    and "file1" in col
                    and "hv" in col.lower()
                ]
                slave_cols_file1 = [
                    col
                    for col in vehicle_record.index
                    if "slave" in col.lower() and "file1" in col and "hv" in col.lower()
                ]
                erstzulassung_cols_file1 = [
                    col
                    for col in vehicle_record.index
                    if "zulassung" in col.lower() and "file1" in col
                ]

                master = (
                    vehicle_record[master_cols_file1[0]] if master_cols_file1 else ""
                )
                slave = vehicle_record[slave_cols_file1[0]] if slave_cols_file1 else ""
                erstzulassung = (
                    vehicle_record[erstzulassung_cols_file1[0]]
                    if erstzulassung_cols_file1
                    else None
                )

                # Clean battery IDs
                if pd.isna(master) or str(master).strip() in ["", "nan", "None"]:
                    master = ""
                else:
                    master = str(master).strip()

                if pd.isna(slave) or str(slave).strip() in ["", "nan", "None"]:
                    slave = ""
                else:
                    slave = str(slave).strip()

                # Create note for date mismatch
                date1 = date_mismatch["date_battery_id"]
                date2 = date_mismatch["date_first_zulassung"]
                note = f"Date mismatch: battery-id file has {date1.strftime('%Y-%m-%d') if pd.notna(date1) else 'N/A'}, first-zulassung file has {date2.strftime('%Y-%m-%d') if pd.notna(date2) else 'N/A'}"

                mismatch_record = {
                    "akz": akz,
                    "vin": vin,
                    "master": master,
                    "slave": slave,
                    "erstzulassung": erstzulassung,
                    "note": note,
                    "mismatch_type": "date",
                }
                true_mismatch_data.append(
                    mismatch_record
                )  # Keep all date mismatches for now

        # Get vehicles with battery mismatches and check against HV repair data
        if self.battery_comparison is not None and not self.battery_comparison.empty:
            battery_mismatches = self.battery_comparison[
                self.battery_comparison["has_mismatch"] == True
            ]

            logger.info(
                f"Checking {len(battery_mismatches)} battery mismatches against HV repair data..."
            )

            for _, battery_mismatch in battery_mismatches.iterrows():
                akz = battery_mismatch["akz"]
                vin = battery_mismatch["vin"]
                erstzulassung = battery_mismatch["erstzulassung"]

                # Get battery IDs from both files
                master_file1 = battery_mismatch["master_battery_id_file"]
                slave_file1 = battery_mismatch["slave_battery_id_file"]
                master_file2 = battery_mismatch["master_first_zulassung_file"]
                slave_file2 = battery_mismatch["slave_first_zulassung_file"]

                # Check against HV repair data
                repair_analysis = self.check_battery_mismatch_against_repairs(
                    vin, master_file1, slave_file1, master_file2, slave_file2
                )

                if repair_analysis["is_repair_related"]:
                    # This is a repair-related difference, recover to working vehicles
                    logger.info(f"Recovering VIN {vin} due to HV repair history")

                    # Use latest battery IDs if available, otherwise use file1 (battery-id file)
                    latest_master = (
                        repair_analysis.get("latest_master")
                        or str(master_file1).strip()
                    )
                    latest_slave = str(slave_file1).strip()  # Simplified logic

                    recovered_record = {
                        "akz": akz,
                        "vin": vin,
                        "master": (
                            latest_master
                            if latest_master
                            and latest_master not in ["", "nan", "None"]
                            else ""
                        ),
                        "slave": (
                            latest_slave
                            if latest_slave and latest_slave not in ["", "nan", "None"]
                            else ""
                        ),
                        "erstzulassung": erstzulassung,
                        "note": f"Recovered after HV repair analysis: {repair_analysis['explanation']}",
                    }
                    recovered_vehicles_data.append(recovered_record)
                else:
                    # This is a true mismatch
                    # Use battery IDs from battery-id file (file1)
                    master = (
                        str(master_file1).strip()
                        if master_file1
                        and str(master_file1).strip() not in ["", "nan", "None"]
                        else ""
                    )
                    slave = (
                        str(slave_file1).strip()
                        if slave_file1
                        and str(slave_file1).strip() not in ["", "nan", "None"]
                        else ""
                    )

                    note = f"TRUE Battery mismatch: {battery_mismatch['mismatch_description']}; {repair_analysis['explanation']}"

                    mismatch_record = {
                        "akz": akz,
                        "vin": vin,
                        "master": master,
                        "slave": slave,
                        "erstzulassung": erstzulassung,
                        "note": note,
                        "mismatch_type": "battery",
                    }
                    true_mismatch_data.append(mismatch_record)

        # Store recovered vehicles for use in generate_working_csv_matching_vehicles
        self.recovered_vehicles = pd.DataFrame(recovered_vehicles_data)
        if not self.recovered_vehicles.empty:
            logger.info(
                f"Recovered {len(self.recovered_vehicles)} vehicles due to HV repair analysis"
            )

        # Process true mismatches
        mismatch_df = pd.DataFrame(true_mismatch_data)

        # Remove duplicates (some vehicles might have both date and battery mismatches)
        if not mismatch_df.empty:
            # For vehicles with multiple mismatch types, combine the notes
            mismatch_df_grouped = (
                mismatch_df.groupby(["akz", "vin"])
                .agg(
                    {
                        "master": "first",
                        "slave": "first",
                        "erstzulassung": "first",
                        "note": lambda x: "; ".join(x),
                        "mismatch_type": lambda x: ", ".join(sorted(set(x))),
                    }
                )
                .reset_index()
            )

            # Drop the mismatch_type column for final output
            mismatch_df_final = mismatch_df_grouped.drop(columns=["mismatch_type"])
        else:
            mismatch_df_final = (
                mismatch_df.drop(columns=["mismatch_type"])
                if "mismatch_type" in mismatch_df.columns
                else mismatch_df
            )

        logger.info(
            f"Generated working CSV with {len(mismatch_df_final)} TRUE mismatch records"
        )
        logger.info(
            f"Recovered {len(recovered_vehicles_data)} vehicles due to HV repair analysis"
        )

        return mismatch_df_final

    def generate_working_csvs_only(
        self, output_dir: str = "comparison_results"
    ) -> Dict[str, str]:
        """Generate only the working CSV files in the requested format."""
        logger.info("Generating working CSV files only...")

        # Create output directory
        Path(output_dir).mkdir(exist_ok=True)

        exported_files = {}

        try:
            # Step 1: Load and clean data
            self.load_excel_files()
            self.clean_and_standardize_data()

            # Step 1.5: Load HV repair data
            self.load_hv_repair_data()

            # Step 2: Find matching vehicles and run comparisons
            self.find_matching_vehicles()
            self.find_unique_vehicles()
            self.compare_erstzulassung_dates()
            self.compare_battery_ids()

            # Step 3: Generate working CSV files
            # Generate matching vehicles working CSV (includes recovered vehicles)
            working_matching = self.generate_working_csv_matching_vehicles()
            if not working_matching.empty:
                working_matching_path = f"{output_dir}/working_matching_vehicles.csv"
                working_matching.to_csv(working_matching_path, index=False)
                exported_files["working_matching_vehicles"] = working_matching_path
                logger.info(
                    f"Exported working matching vehicles CSV: {working_matching_path} ({len(working_matching)} records)"
                )

            # Generate unique vehicles working CSV
            working_unique = self.generate_working_csv_unique_vehicles()
            if not working_unique.empty:
                working_unique_path = f"{output_dir}/working_unique_vehicles.csv"
                working_unique.to_csv(working_unique_path, index=False)
                exported_files["working_unique_vehicles"] = working_unique_path
                logger.info(
                    f"Exported working unique vehicles CSV: {working_unique_path} ({len(working_unique)} records)"
                )

            # Generate mismatches working CSV (only TRUE mismatches)
            working_mismatches = self.generate_working_csv_mismatches()
            if not working_mismatches.empty:
                working_mismatches_path = f"{output_dir}/working_mismatches.csv"
                working_mismatches.to_csv(working_mismatches_path, index=False)
                exported_files["working_mismatches"] = working_mismatches_path
                logger.info(
                    f"Exported working mismatches CSV: {working_mismatches_path} ({len(working_mismatches)} records)"
                )
            else:
                logger.info("No TRUE mismatches found - no mismatch CSV generated")

            # Export recovered vehicles summary (for transparency)
            if (
                hasattr(self, "recovered_vehicles")
                and not self.recovered_vehicles.empty
            ):
                recovered_path = f"{output_dir}/recovered_vehicles_from_hv_repair.csv"
                self.recovered_vehicles.to_csv(recovered_path, index=False)
                exported_files["recovered_vehicles"] = recovered_path
                logger.info(
                    f"Exported recovered vehicles CSV: {recovered_path} ({len(self.recovered_vehicles)} records)"
                )

            # Step 4: Validate complete coverage
            logger.info("\n" + "=" * 60)
            logger.info("VALIDATING COMPLETE COVERAGE")
            logger.info("=" * 60)

            validation_results = self.validate_complete_coverage()

            # Export validation results
            validation_path = f"{output_dir}/coverage_validation.txt"
            with open(validation_path, "w") as f:
                f.write("Vehicle Coverage Validation Report\n")
                f.write("=" * 40 + "\n\n")

                f.write(
                    f"Validation Status: {validation_results['validation_status']}\n"
                )
                f.write(
                    f"Coverage: {validation_results['coverage_statistics']['coverage_percentage']:.2f}%\n\n"
                )

                f.write("Coverage Statistics:\n")
                for key, value in validation_results["coverage_statistics"].items():
                    f.write(f"  {key}: {value}\n")

                f.write("\nFile Breakdown:\n")
                for key, value in validation_results["file_breakdown"].items():
                    f.write(f"  {key}: {value:,}\n")

                f.write("\nOverlap Analysis:\n")
                for key, value in validation_results["overlap_analysis"].items():
                    f.write(f"  {key}: {value}\n")

                # Add HV repair analysis summary
                if (
                    hasattr(self, "recovered_vehicles")
                    and not self.recovered_vehicles.empty
                ):
                    f.write(f"\nHV Repair Recovery Analysis:\n")
                    f.write(
                        f"  Vehicles recovered due to HV repairs: {len(self.recovered_vehicles)}\n"
                    )
                    f.write(
                        f"  These vehicles were moved from mismatches to matching vehicles\n"
                    )
                    f.write(
                        f"  Recovery indicates battery differences explained by repair history\n"
                    )

                if validation_results["missing_vehicles"]:
                    f.write(f"\nMissing Vehicles (first 20):\n")
                    for vehicle in validation_results["missing_vehicles"]:
                        f.write(f"  {vehicle}\n")

                if validation_results["extra_vehicles"]:
                    f.write(f"\nExtra Vehicles (first 20):\n")
                    for vehicle in validation_results["extra_vehicles"]:
                        f.write(f"  {vehicle}\n")

            exported_files["coverage_validation"] = validation_path
            logger.info(f"Exported coverage validation report: {validation_path}")

            # Export HV repair analysis summary
            if hasattr(self, "hv_repair_data") and not self.hv_repair_data.empty:
                hv_analysis_path = f"{output_dir}/hv_repair_analysis_summary.txt"
                with open(hv_analysis_path, "w") as f:
                    f.write("HV Repair Analysis Summary\n")
                    f.write("=" * 30 + "\n\n")

                    f.write(f"Total HV repair records: {len(self.hv_repair_data)}\n")
                    f.write(
                        f"Unique vehicles with repair history: {self.hv_repair_data['vin'].nunique()}\n"
                    )

                    if (
                        hasattr(self, "recovered_vehicles")
                        and not self.recovered_vehicles.empty
                    ):
                        f.write(
                            f"Vehicles recovered due to repair analysis: {len(self.recovered_vehicles)}\n"
                        )
                        f.write(
                            f"\nRecovered vehicles moved from mismatches to working vehicles.\n"
                        )
                        f.write(
                            f"This indicates their battery differences were due to different\n"
                        )
                        f.write(
                            f"snapshots in time (before/after battery repairs/replacements).\n"
                        )
                    else:
                        f.write(
                            f"No vehicles were recovered through repair analysis.\n"
                        )

                exported_files["hv_repair_analysis"] = hv_analysis_path
                logger.info(f"Exported HV repair analysis summary: {hv_analysis_path}")

            logger.info(
                "\nWorking CSV generation with HV repair analysis completed successfully!"
            )

        except Exception as e:
            logger.error(f"Working CSV generation failed: {e}")
            raise

        return exported_files

    def validate_complete_coverage(self) -> Dict:
        """Validate that all vehicles from both files are covered across the three working CSV files."""
        logger.info("Validating complete coverage across all working CSV files...")

        # Generate all three working datasets
        matching_df = self.generate_working_csv_matching_vehicles()
        unique_df = self.generate_working_csv_unique_vehicles()
        mismatch_df = self.generate_working_csv_mismatches()

        # Get all unique vehicles from original files
        if self.df1 is None or self.df2 is None:
            self.load_excel_files()
            self.clean_and_standardize_data()

        # Create sets of vehicle keys from original files
        file1_vehicles = set()
        file2_vehicles = set()

        # Build keys from file1 (battery-id)
        for _, row in self.df1.iterrows():
            akz = str(row.get("akz", "")).strip().upper()
            vin = str(row.get("vin", "")).strip().upper()
            if akz not in ["", "NAN", "NONE"] and vin not in ["", "NAN", "NONE"]:
                key = f"{akz}|{vin}"
                file1_vehicles.add(key)

        # Build keys from file2 (first-zulassung)
        for _, row in self.df2.iterrows():
            akz = str(row.get("akz", "")).strip().upper()
            vin = str(row.get("vin", "")).strip().upper()
            if akz not in ["", "NAN", "NONE"] and vin not in ["", "NAN", "NONE"]:
                key = f"{akz}|{vin}"
                file2_vehicles.add(key)

        # Build keys from working CSV files
        matching_vehicles = set()
        unique_vehicles = set()
        mismatch_vehicles = set()

        # Process matching vehicles
        for _, row in matching_df.iterrows():
            akz = str(row.get("akz", "")).strip().upper()
            vin = str(row.get("vin", "")).strip().upper()
            if akz not in ["", "NAN", "NONE"] and vin not in ["", "NAN", "NONE"]:
                key = f"{akz}|{vin}"
                matching_vehicles.add(key)

        # Process unique vehicles
        for _, row in unique_df.iterrows():
            akz = str(row.get("akz", "")).strip().upper()
            vin = str(row.get("vin", "")).strip().upper()
            if akz not in ["", "NAN", "NONE"] and vin not in ["", "NAN", "NONE"]:
                key = f"{akz}|{vin}"
                unique_vehicles.add(key)

        # Process mismatch vehicles
        for _, row in mismatch_df.iterrows():
            akz = str(row.get("akz", "")).strip().upper()
            vin = str(row.get("vin", "")).strip().upper()
            if akz not in ["", "NAN", "NONE"] and vin not in ["", "NAN", "NONE"]:
                key = f"{akz}|{vin}"
                mismatch_vehicles.add(key)

        # Combine all working CSV vehicles
        all_working_vehicles = matching_vehicles.union(unique_vehicles).union(
            mismatch_vehicles
        )

        # Combine all original vehicles
        all_original_vehicles = file1_vehicles.union(file2_vehicles)

        # Find missing vehicles
        missing_from_working = all_original_vehicles - all_working_vehicles
        extra_in_working = all_working_vehicles - all_original_vehicles

        # Check for overlaps between working files (should be none)
        matching_unique_overlap = matching_vehicles.intersection(unique_vehicles)
        matching_mismatch_overlap = matching_vehicles.intersection(mismatch_vehicles)
        unique_mismatch_overlap = unique_vehicles.intersection(mismatch_vehicles)

        # Calculate statistics
        total_original = len(all_original_vehicles)
        total_working = len(all_working_vehicles)
        coverage_percentage = (
            (
                len(all_working_vehicles.intersection(all_original_vehicles))
                / total_original
                * 100
            )
            if total_original > 0
            else 0
        )

        validation_results = {
            "validation_status": (
                "PASS"
                if len(missing_from_working) == 0 and len(extra_in_working) == 0
                else "FAIL"
            ),
            "coverage_statistics": {
                "total_original_vehicles": total_original,
                "total_working_vehicles": total_working,
                "coverage_percentage": round(coverage_percentage, 2),
                "missing_vehicles_count": len(missing_from_working),
                "extra_vehicles_count": len(extra_in_working),
            },
            "file_breakdown": {
                "file1_battery_id_vehicles": len(file1_vehicles),
                "file2_first_zulassung_vehicles": len(file2_vehicles),
                "matching_vehicles": len(matching_vehicles),
                "unique_vehicles": len(unique_vehicles),
                "mismatch_vehicles": len(mismatch_vehicles),
            },
            "overlap_analysis": {
                "matching_unique_overlap": len(matching_unique_overlap),
                "matching_mismatch_overlap": len(matching_mismatch_overlap),
                "unique_mismatch_overlap": len(unique_mismatch_overlap),
                "total_overlaps": len(matching_unique_overlap)
                + len(matching_mismatch_overlap)
                + len(unique_mismatch_overlap),
            },
            "missing_vehicles": (
                list(missing_from_working)[:20] if missing_from_working else []
            ),  # Show first 20
            "extra_vehicles": (
                list(extra_in_working)[:20] if extra_in_working else []
            ),  # Show first 20
        }

        # Log results
        logger.info("Coverage Validation Results:")
        logger.info(f"  Validation Status: {validation_results['validation_status']}")
        logger.info(
            f"  Coverage: {coverage_percentage:.2f}% ({len(all_working_vehicles.intersection(all_original_vehicles))}/{total_original})"
        )

        if validation_results["validation_status"] == "PASS":
            logger.info(
                "  ✅ All vehicles are properly covered across the three working CSV files!"
            )
        else:
            logger.warning(f"  ❌ Coverage issues found:")
            if missing_from_working:
                logger.warning(
                    f"    - {len(missing_from_working)} vehicles missing from working CSV files"
                )
            if extra_in_working:
                logger.warning(
                    f"    - {len(extra_in_working)} extra vehicles in working CSV files"
                )

        logger.info("  File Distribution:")
        logger.info(f"    - Matching vehicles: {len(matching_vehicles):,}")
        logger.info(f"    - Unique vehicles: {len(unique_vehicles):,}")
        logger.info(f"    - Mismatch vehicles: {len(mismatch_vehicles):,}")

        if validation_results["overlap_analysis"]["total_overlaps"] > 0:
            logger.warning(
                f"  ⚠️  Found {validation_results['overlap_analysis']['total_overlaps']} overlapping vehicles between working files"
            )
        else:
            logger.info("  ✅ No overlaps between working CSV files")

        return validation_results


def main():
    """Main function to run the vehicle-battery comparison."""
    import sys

    # File paths
    file1_path = "Fahrzeug Batterie Zuordnung-with-batteryid.xlsx"
    file2_path = "Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx"

    # Check if files exist
    if not Path(file1_path).exists():
        logger.error(f"File not found: {file1_path}")
        return

    if not Path(file2_path).exists():
        logger.error(f"File not found: {file2_path}")
        return

    # Create comparator
    comparator = VehicleBatteryComparator(file1_path, file2_path)

    # Check if user wants just working CSV files
    if len(sys.argv) > 1 and sys.argv[1] == "--working-csv-only":
        logger.info("Generating working CSV files only...")
        try:
            exported_files = comparator.generate_working_csvs_only()
            logger.info("Working CSV files generated successfully:")
            for file_type, file_path in exported_files.items():
                logger.info(f"  {file_type}: {file_path}")
        except Exception as e:
            logger.error(f"Failed to generate working CSV files: {e}")
        return

    # Run complete analysis
    results = comparator.run_complete_analysis()

    if results["success"]:
        logger.info("=" * 50)
        logger.info("ANALYSIS SUMMARY")
        logger.info("=" * 50)

        stats = results["summary_stats"]

        logger.info(f"Data Overview:")
        for key, value in stats["data_overview"].items():
            logger.info(f"  {key}: {value:,}")

        if stats["date_analysis"]:
            logger.info(f"\nDate Analysis:")
            for key, value in stats["date_analysis"].items():
                logger.info(f"  {key}: {value}")

        if stats["battery_analysis"]:
            logger.info(f"\nBattery Analysis:")
            for key, value in stats["battery_analysis"].items():
                if isinstance(value, dict):
                    logger.info(f"  {key}:")
                    for subkey, subvalue in value.items():
                        logger.info(f"    {subkey}: {subvalue}")
                else:
                    logger.info(f"  {key}: {value}")

        if "unique_vehicles" in stats and stats["unique_vehicles"]:
            logger.info(f"\nUnique Vehicles:")
            for key, value in stats["unique_vehicles"].items():
                logger.info(f"  {key}: {value}")

        logger.info(f"\nExported files:")
        for file_type, file_path in results["exported_files"].items():
            logger.info(f"  {file_type}: {file_path}")

    else:
        logger.error(f"Analysis failed: {results['error']}")


if __name__ == "__main__":
    main()
