#!/usr/bin/env python3
"""
Battery Kilometers Calculator

This script calculates the total kilometers each battery has traveled
by analyzing vehicle odometer data and battery change history.
"""

import pandas as pd
import psycopg2
from sqlalchemy import create_engine
import numpy as np
from datetime import datetime, date
import logging
from typing import Optional, Tuple

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BatteryKmCalculator:
    """Calculate total kilometers traveled by each battery across all vehicles."""
    
    def __init__(self, db_connection_string: str, csv_file_path: str):
        """
        Initialize the calculator with database connection and CSV file path.
        
        Args:
            db_connection_string: PostgreSQL connection string
            csv_file_path: Path to the battery repair/change CSV file
        """
        self.db_connection_string = db_connection_string
        self.csv_file_path = csv_file_path
        self.engine = None
        
    def connect_to_database(self) -> bool:
        """Establish database connection."""
        try:
            self.engine = create_engine(self.db_connection_string)
            # Test connection
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def load_vehicle_data(self) -> pd.DataFrame:
        """Load vehicle data from database."""
        query = """
        SELECT vehicle_id, vin, depot_id, usable_battery_capacity
        FROM vehicles
        WHERE vin IS NOT NULL
        """
        return pd.read_sql(query, self.engine)
    
    def load_daily_stats(self) -> pd.DataFrame:
        """Load daily statistics with odometer readings."""
        query = """
        SELECT 
            vehicle_id,
            date,
            km_end,
            timestamp_end,
            timestamp_start
        FROM daily_stats
        WHERE km_end IS NOT NULL
        ORDER BY vehicle_id, date
        """
        return pd.read_sql(query, self.engine)
    
    def load_battery_changes(self) -> pd.DataFrame:
        """Load and clean battery change data from CSV."""
        df = pd.read_csv(self.csv_file_path, sep=';')
        
        # Clean the data
        df = df.copy()
        
        # Convert dates
        df['created'] = pd.to_datetime(df['created'], errors='coerce')
        df['battery_changed'] = df['battery_changed'].replace('--', None)
        df['battery_changed'] = pd.to_datetime(df['battery_changed'], errors='coerce')
        
        # Use actual change date if available, otherwise use created date
        df['effective_date'] = df['battery_changed'].fillna(df['created'])
        
        # Filter out rows without valid data
        df = df.dropna(subset=['vin', 'effective_date'])
        
        # Sort by VIN and date
        df = df.sort_values(['vin', 'effective_date'])
        
        logger.info(f"Loaded {len(df)} battery change records")
        return df
    
    def create_battery_timeline(self, battery_changes: pd.DataFrame) -> pd.DataFrame:
        """Create timeline of battery installations per vehicle."""
        timeline_records = []
        
        for vin in battery_changes['vin'].unique():
            vehicle_changes = battery_changes[battery_changes['vin'] == vin].copy()
            vehicle_changes = vehicle_changes.sort_values('effective_date')
            
            for i, row in vehicle_changes.iterrows():
                # Determine the period this battery was active
                start_date = row['effective_date']
                
                # Find next change for this vehicle
                next_changes = vehicle_changes[vehicle_changes['effective_date'] > start_date]
                end_date = next_changes['effective_date'].min() if not next_changes.empty else None
                
                # Only process if we have a new battery being installed
                if pd.notna(row['battery_id_new']) and str(row['battery_id_new']).strip():
                    timeline_records.append({
                        'vin': vin,
                        'battery_id': str(row['battery_id_new']).strip(),
                        'start_date': start_date,
                        'end_date': end_date,
                        'action': row['action'],
                        'battery_type': row.get('battery_type_new', '')
                    })
        
        timeline_df = pd.DataFrame(timeline_records)
        logger.info(f"Created timeline with {len(timeline_df)} battery periods")
        return timeline_df
    
    def calculate_km_per_battery_period(self, timeline: pd.DataFrame, 
                                      vehicles: pd.DataFrame, 
                                      daily_stats: pd.DataFrame) -> pd.DataFrame:
        """Calculate kilometers for each battery period."""
        # Merge timeline with vehicle data to get vehicle_id
        timeline_with_vehicles = timeline.merge(vehicles[['vin', 'vehicle_id']], on='vin', how='left')
        
        km_records = []
        
        for _, period in timeline_with_vehicles.iterrows():
            if pd.isna(period['vehicle_id']):
                continue
                
            # Get odometer readings for this vehicle during this period
            vehicle_stats = daily_stats[daily_stats['vehicle_id'] == period['vehicle_id']].copy()
            
            # Filter by date range
            period_stats = vehicle_stats[vehicle_stats['date'] >= period['start_date'].date()]
            
            if period['end_date'] is not None:
                period_stats = period_stats[period_stats['date'] < period['end_date'].date()]
            
            if len(period_stats) > 0:
                km_start = period_stats['km_end'].min()
                km_end = period_stats['km_end'].max()
                km_traveled = km_end - km_start if km_end > km_start else 0
                
                km_records.append({
                    'battery_id': period['battery_id'],
                    'vin': period['vin'],
                    'vehicle_id': period['vehicle_id'],
                    'start_date': period['start_date'],
                    'end_date': period['end_date'],
                    'km_start': km_start,
                    'km_end': km_end,
                    'km_traveled': km_traveled,
                    'days_active': len(period_stats),
                    'battery_type': period['battery_type']
                })
        
        km_df = pd.DataFrame(km_records)
        logger.info(f"Calculated kilometers for {len(km_df)} battery periods")
        return km_df
    
    def aggregate_battery_totals(self, km_periods: pd.DataFrame) -> pd.DataFrame:
        """Aggregate total kilometers per battery across all vehicles."""
        battery_totals = km_periods.groupby('battery_id').agg({
            'km_traveled': 'sum',
            'vin': 'nunique',
            'start_date': 'min',
            'end_date': 'max',
            'days_active': 'sum',
            'battery_type': 'first'
        }).reset_index()
        
        battery_totals.columns = [
            'battery_id', 
            'total_km_traveled', 
            'vehicles_used', 
            'first_installation', 
            'last_usage',
            'total_days_active',
            'battery_type'
        ]
        
        # Add current status
        current_date = pd.Timestamp.now()
        battery_totals['currently_in_use'] = battery_totals['last_usage'].isna()
        
        # Sort by total kilometers
        battery_totals = battery_totals.sort_values('total_km_traveled', ascending=False)
        
        logger.info(f"Aggregated data for {len(battery_totals)} unique batteries")
        return battery_totals
    
    def run_analysis(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Run the complete battery kilometers analysis."""
        logger.info("Starting battery kilometers analysis...")
        
        # Connect to database
        if not self.connect_to_database():
            raise Exception("Failed to connect to database")
        
        # Load data
        logger.info("Loading data...")
        vehicles = self.load_vehicle_data()
        daily_stats = self.load_daily_stats()
        battery_changes = self.load_battery_changes()
        
        # Create battery timeline
        logger.info("Creating battery timeline...")
        timeline = self.create_battery_timeline(battery_changes)
        
        # Calculate kilometers per period
        logger.info("Calculating kilometers per battery period...")
        km_periods = self.calculate_km_per_battery_period(timeline, vehicles, daily_stats)
        
        # Aggregate totals
        logger.info("Aggregating battery totals...")
        battery_totals = self.aggregate_battery_totals(km_periods)
        
        logger.info("Analysis complete!")
        return battery_totals, km_periods


def main():
    """Main function to run the analysis."""
    # Database connection string - MODIFY THIS WITH YOUR DATABASE CREDENTIALS
    db_connection = "postgresql://username:password@localhost:5432/database_name"
    
    # CSV file path
    csv_file = "hv_repair_2025-06-02b.csv"
    
    # Initialize calculator
    calculator = BatteryKmCalculator(db_connection, csv_file)
    
    try:
        # Run analysis
        battery_totals, km_periods = calculator.run_analysis()
        
        # Display results
        print("\n" + "="*80)
        print("BATTERY KILOMETERS ANALYSIS RESULTS")
        print("="*80)
        
        print(f"\nTop 10 batteries by total kilometers:")
        print(battery_totals.head(10)[['battery_id', 'total_km_traveled', 'vehicles_used', 'battery_type']].to_string(index=False))
        
        print(f"\nSummary Statistics:")
        print(f"Total unique batteries analyzed: {len(battery_totals)}")
        print(f"Total kilometers across all batteries: {battery_totals['total_km_traveled'].sum():,.0f} km")
        print(f"Average kilometers per battery: {battery_totals['total_km_traveled'].mean():,.0f} km")
        print(f"Batteries currently in use: {battery_totals['currently_in_use'].sum()}")
        
        # Save results
        battery_totals.to_csv('battery_totals_analysis.csv', index=False)
        km_periods.to_csv('battery_periods_detail.csv', index=False)
        
        print(f"\nResults saved to:")
        print(f"- battery_totals_analysis.csv")
        print(f"- battery_periods_detail.csv")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main() 