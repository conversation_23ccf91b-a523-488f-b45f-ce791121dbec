#!/usr/bin/env python3
"""
Battery Age Calculator

A comprehensive system for calculating battery ages based on their complete timeline data.
Processes battery timeline CSV files, fills missing end dates for active batteries using
daily stats data, and exports results with proper error handling.

This module provides a complete solution for analyzing battery lifecycle data across
electric vehicle fleets. It handles complex scenarios including:

- Multiple battery periods per battery (battery transfers between vehicles)
- Missing start/end dates with intelligent fallback logic
- Active battery end date filling using latest daily stats
- Database integration for VIN mapping and daily stats queries
- Comprehensive error handling and data validation
- Performance optimization for large datasets

Key Classes:
    BatteryAgeCalculator: Main orchestrator class for the calculation process
    TimelineLoader: Handles CSV loading and validation
    DatabaseConnector: Manages database connections and queries
    BatteryGrouper: Groups timeline records by battery_id
    AgeCalculator: Core age calculation logic with error handling
    ActiveBatteryProcessor: Fills missing end dates for active batteries
    ResultExporter: Exports results to CSV with proper formatting

Usage Examples:
    Basic usage:
        calculator = BatteryAgeCalculator("timeline.csv", "daily_stats.csv")
        results = calculator.calculate_battery_ages()
        calculator.export_results("output.csv")

    With database connection:
        calculator = BatteryAgeCalculator(
            "timeline.csv",
            "daily_stats.csv",
            "postgresql://user:pass@host:port/db"
        )
        results = calculator.calculate_battery_ages()

    Command line usage:
        python battery_age_calculator.py timeline.csv daily_stats.csv output.csv

Data Format Requirements:
    Timeline CSV must contain columns:
        - battery_id: Unique identifier for each battery
        - vin: Vehicle identification number
        - start_date: Start date of battery period (YYYY-MM-DD format)
        - end_date: End date of battery period (YYYY-MM-DD format, can be empty)
        - is_currently_active: Boolean indicating if battery is currently active

    Daily Stats CSV must contain columns:
        - vehicle_id: Unique vehicle identifier
        - date: Date of the daily stats record
        - km_start: Starting odometer reading
        - km_end: Ending odometer reading

Performance Considerations:
    - CSV pre-indexing provides O(1) daily stats lookups
    - Batch database queries minimize connection overhead
    - Memory-efficient processing for large datasets
    - Progress reporting for long-running operations

Error Handling:
    - Comprehensive validation of input data
    - Graceful handling of missing dates and invalid ranges
    - Database connection fallbacks and retry logic
    - Detailed error reporting in output CSV

Author: Battery Analysis System
Version: 1.0
"""

import logging
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import config


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("battery_age_calculator.log"),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class BatteryTimeline:
    """Data model for battery timeline input records."""

    battery_id: str
    vin: str
    start_date: Optional[pd.Timestamp]
    end_date: Optional[pd.Timestamp]
    is_currently_active: bool


@dataclass
class BatteryAge:
    """Data model for calculated battery age results."""

    battery_id: str
    earliest_start: Optional[pd.Timestamp]
    latest_end: Optional[pd.Timestamp]
    age_days: Optional[float]
    note: str = ""


@dataclass
class VINMapping:
    """Data model for VIN to vehicle_id mapping."""

    vin: str
    vehicle_id: int


class BatteryAgeCalculatorError(Exception):
    """Custom exception for battery age calculation errors."""

    pass


class DataValidationError(BatteryAgeCalculatorError):
    """Exception raised for data validation errors."""

    pass


class DatabaseConnectionError(BatteryAgeCalculatorError):
    """Exception raised for database connection issues."""

    pass


class TimelineLoader:
    """
    Handles loading and validation of battery timeline data from CSV files.

    This class provides robust CSV loading with proper data type handling,
    column validation, and date parsing with comprehensive error handling.

    The loader validates that the CSV contains all required columns:
    - battery_id: Unique identifier for each battery
    - vin: Vehicle identification number
    - start_date: Start date of battery period
    - end_date: End date of battery period (can be empty)
    - is_currently_active: Boolean indicating if battery is currently active

    Usage Examples:
        Basic loading:
            >>> loader = TimelineLoader("battery_timeline.csv")
            >>> timeline_data = loader.load_timeline_data()
            >>> print(f"Loaded {len(timeline_data)} timeline records")

        With error handling:
            >>> try:
            ...     loader = TimelineLoader("timeline.csv")
            ...     data = loader.load_timeline_data()
            ...     print(f"Successfully loaded {len(data)} records")
            ... except DataValidationError as e:
            ...     print(f"Data validation failed: {e}")

        Accessing loaded data:
            >>> loader = TimelineLoader("timeline.csv")
            >>> data = loader.load_timeline_data()
            >>> unique_batteries = data['battery_id'].nunique()
            >>> active_batteries = data[data['is_currently_active']].shape[0]
            >>> print(f"Found {unique_batteries} unique batteries, {active_batteries} active")

    Attributes:
        csv_path (str): Path to the battery timeline CSV file
        data (Optional[pd.DataFrame]): Loaded and validated timeline data
    """

    def __init__(self, csv_path: str):
        """
        Initialize the TimelineLoader.

        Args:
            csv_path: Path to the battery timeline CSV file
        """
        self.csv_path = csv_path
        self.data: Optional[pd.DataFrame] = None

    def load_timeline_data(self) -> pd.DataFrame:
        """
        Load battery timeline data from CSV with validation and error handling.

        Returns:
            DataFrame containing validated timeline data

        Raises:
            DataValidationError: If file not found, missing columns, or data validation fails
        """
        try:
            logger.info(f"Loading timeline data from: {self.csv_path}")

            # Check if file exists
            if not os.path.exists(self.csv_path):
                raise DataValidationError(
                    f"Timeline CSV file not found: {self.csv_path}"
                )

            # Load CSV with proper handling
            try:
                # First, try to load without date parsing to check structure
                self.data = pd.read_csv(self.csv_path)
                logger.info(f"Loaded {len(self.data)} raw timeline records")

            except Exception as e:
                raise DataValidationError(f"Failed to read CSV file: {str(e)}")

            # Validate required columns
            self._validate_required_columns()

            # Parse and validate dates
            self._parse_and_validate_dates()

            # Validate data types and content
            self._validate_data_content()

            logger.info(f"Successfully validated {len(self.data)} timeline records")
            return self.data

        except DataValidationError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error loading timeline data: {str(e)}")
            raise DataValidationError(f"Timeline data loading failed: {str(e)}")

    def _validate_required_columns(self) -> None:
        """Validate that all required columns are present in the CSV."""
        required_columns = [
            "battery_id",
            "vin",
            "start_date",
            "end_date",
            "is_currently_active",
        ]
        missing_columns = [
            col for col in required_columns if col not in self.data.columns
        ]

        if missing_columns:
            available_columns = list(self.data.columns)
            logger.error(f"Missing required columns: {missing_columns}")
            logger.error(f"Available columns: {available_columns}")
            raise DataValidationError(f"Missing required columns: {missing_columns}")

        logger.info("All required columns present")

    def _parse_and_validate_dates(self) -> None:
        """Parse date columns with comprehensive error handling."""
        date_columns = ["start_date", "end_date"]

        for col in date_columns:
            if col in self.data.columns:
                try:
                    # Convert to datetime, handling various formats and errors
                    original_count = len(self.data)

                    # Use pandas to_datetime with error handling
                    self.data[col] = pd.to_datetime(self.data[col], errors="coerce")

                    # Count how many dates failed to parse
                    failed_dates = self.data[col].isna().sum()
                    if failed_dates > 0:
                        logger.warning(
                            f"Failed to parse {failed_dates} dates in column '{col}'"
                        )

                    logger.info(
                        f"Parsed {col}: {original_count - failed_dates}/{original_count} successful"
                    )

                except Exception as e:
                    logger.error(f"Error parsing {col}: {str(e)}")
                    raise DataValidationError(
                        f"Date parsing failed for column {col}: {str(e)}"
                    )

    def _validate_data_content(self) -> None:
        """Validate data content and types."""
        try:
            # Validate battery_id column
            if self.data["battery_id"].isna().any():
                na_count = self.data["battery_id"].isna().sum()
                logger.warning(f"Found {na_count} missing battery_id values")

            # Validate VIN column
            if self.data["vin"].isna().any():
                na_count = self.data["vin"].isna().sum()
                logger.warning(f"Found {na_count} missing VIN values")

            # Validate and convert is_currently_active to boolean
            if "is_currently_active" in self.data.columns:
                try:
                    # Handle various boolean representations
                    self.data["is_currently_active"] = self.data[
                        "is_currently_active"
                    ].astype(bool)
                except Exception as e:
                    logger.warning(
                        f"Error converting is_currently_active to boolean: {str(e)}"
                    )
                    # Try to handle string representations
                    self.data["is_currently_active"] = self.data[
                        "is_currently_active"
                    ].map(
                        lambda x: (
                            str(x).lower() in ["true", "1", "yes", "active"]
                            if pd.notna(x)
                            else False
                        )
                    )

            # Log data quality summary
            total_records = len(self.data)
            unique_batteries = self.data["battery_id"].nunique()
            unique_vins = self.data["vin"].nunique()
            active_batteries = (
                self.data["is_currently_active"].sum()
                if "is_currently_active" in self.data.columns
                else 0
            )

            logger.info(f"Data quality summary:")
            logger.info(f"  Total records: {total_records}")
            logger.info(f"  Unique batteries: {unique_batteries}")
            logger.info(f"  Unique VINs: {unique_vins}")
            logger.info(f"  Currently active batteries: {active_batteries}")

        except Exception as e:
            logger.error(f"Data content validation failed: {str(e)}")
            raise DataValidationError(f"Data content validation failed: {str(e)}")


class DatabaseConnector:
    """
    Handles PostgreSQL database connections for VIN mapping and CSV-based daily stats loading.

    This class provides robust database connectivity for VIN mapping and efficient
    CSV-based daily stats loading for battery age calculation needs.

    The connector supports two main data sources:
    1. PostgreSQL database for VIN to vehicle_id mapping
    2. CSV file for daily stats data (pre-indexed for performance)

    Usage Examples:
        Basic usage with database connection:
            >>> db_conn = DatabaseConnector("postgresql://user:pass@host:port/db")
            >>> if db_conn.connect():
            ...     mappings = db_conn.load_vin_mappings(["VIN001", "VIN002"])
            ...     print(f"Mapped {len(mappings)} VINs")
            ... db_conn.disconnect()

        Using as context manager:
            >>> with DatabaseConnector() as db_conn:
            ...     db_conn.load_daily_stats_csv()
            ...     latest_date = db_conn.get_latest_daily_stats_for_vin("VIN001")
            ...     print(f"Latest activity: {latest_date}")

        Testing connection:
            >>> db_conn = DatabaseConnector()
            >>> if db_conn.test_connection():
            ...     print("Database is accessible")
            ... else:
            ...     print("Database connection failed")

        Batch operations for performance:
            >>> vins = ["VIN001", "VIN002", "VIN003"]
            >>> date_mappings = db_conn.get_latest_daily_stats_for_vins_batch(vins)
            >>> for vin, date in date_mappings.items():
            ...     print(f"{vin}: {date}")

    Attributes:
        connection_string (str): PostgreSQL connection string
        daily_stats_csv_path (str): Path to daily stats CSV file
        engine (Optional[Engine]): SQLAlchemy database engine
        is_connected (bool): Database connection status
        vin_mappings (Dict[str, int]): Cached VIN to vehicle_id mappings
        daily_stats_by_vehicle (Dict[int, pd.DataFrame]): Pre-indexed daily stats data
    """

    def __init__(
        self,
        connection_string: Optional[str] = None,
        daily_stats_csv_path: str = "daily_stats.csv",
    ):
        """
        Initialize the DatabaseConnector.

        Args:
            connection_string: Optional database connection string. If not provided,
                             will use config.DB_CONNECTION_STRING
            daily_stats_csv_path: Path to daily stats CSV file
        """
        self.connection_string = connection_string or config.DB_CONNECTION_STRING
        self.daily_stats_csv_path = daily_stats_csv_path
        self.engine = None
        self.is_connected = False
        self.vin_mappings: Dict[str, int] = {}

        # Daily stats data (loaded from CSV)
        self.daily_stats_df: Optional[pd.DataFrame] = None
        self.daily_stats_by_vehicle: Dict[int, pd.DataFrame] = {}

    def connect(self) -> bool:
        """
        Establish database connection.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            logger.info("Establishing database connection...")
            self.engine = create_engine(self.connection_string)

            # Test connection with a simple query
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()

            self.is_connected = True
            logger.info("Database connection established successfully")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database connection failed: {str(e)}")
            self.is_connected = False
            return False
        except Exception as e:
            logger.error(f"Unexpected database connection error: {str(e)}")
            self.is_connected = False
            return False

    def disconnect(self) -> None:
        """Close database connection."""
        if self.engine:
            try:
                self.engine.dispose()
                self.is_connected = False
                logger.info("Database connection closed")
            except Exception as e:
                logger.warning(f"Error closing database connection: {str(e)}")

    def load_vin_mappings(self, vins: List[str]) -> Dict[str, int]:
        """
        Load VIN to vehicle_id mappings from the vehicles table.

        Args:
            vins: List of VINs to look up

        Returns:
            Dictionary mapping VIN to vehicle_id

        Raises:
            DatabaseConnectionError: If database connection fails
        """
        if not self.is_connected:
            if not self.connect():
                raise DatabaseConnectionError(
                    "Cannot establish database connection for VIN mapping"
                )

        try:
            logger.info(f"Loading VIN mappings for {len(vins)} VINs")

            if not vins:
                logger.warning("No VINs provided for mapping")
                return {}

            # Create parameterized query for batch VIN lookup
            vin_list = "', '".join(vins)
            query = text(
                f"""
                SELECT vin, vehicle_id 
                FROM vehicles 
                WHERE vin IN ('{vin_list}')
            """
            )

            with self.engine.connect() as conn:
                result = conn.execute(query)
                mappings = {}

                for row in result:
                    mappings[row.vin] = row.vehicle_id

                logger.info(
                    f"Successfully mapped {len(mappings)}/{len(vins)} VINs to vehicle_ids"
                )

                # Log missing VINs
                missing_vins = set(vins) - set(mappings.keys())
                if missing_vins:
                    logger.warning(
                        f"Could not find vehicle_id for {len(missing_vins)} VINs"
                    )
                    logger.debug(
                        f"Missing VINs: {list(missing_vins)[:10]}..."
                    )  # Log first 10

                self.vin_mappings.update(mappings)
                return mappings

        except SQLAlchemyError as e:
            logger.error(f"Database error during VIN mapping: {str(e)}")
            raise DatabaseConnectionError(f"VIN mapping query failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during VIN mapping: {str(e)}")
            raise DatabaseConnectionError(f"VIN mapping failed: {str(e)}")

    def get_vehicle_id_for_vin(self, vin: str) -> Optional[int]:
        """
        Get vehicle_id for a single VIN.

        Args:
            vin: VIN to look up

        Returns:
            vehicle_id if found, None otherwise
        """
        # Check cached mappings first
        if vin in self.vin_mappings:
            return self.vin_mappings[vin]

        # Load single VIN mapping
        try:
            mappings = self.load_vin_mappings([vin])
            return mappings.get(vin)
        except DatabaseConnectionError:
            logger.warning(
                f"Could not get vehicle_id for VIN {vin} due to database error"
            )
            return None

    def test_connection(self) -> bool:
        """
        Test database connectivity without establishing persistent connection.

        Returns:
            True if connection test successful, False otherwise
        """
        try:
            test_engine = create_engine(self.connection_string)
            with test_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            test_engine.dispose()
            return True
        except Exception as e:
            logger.debug(f"Database connection test failed: {str(e)}")
            return False

    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()

    def load_daily_stats_csv(self) -> None:
        """Load daily stats data from CSV file and pre-index by vehicle_id for fast lookup."""
        logger.info(f"Loading daily stats data from CSV: {self.daily_stats_csv_path}")

        try:
            # Load CSV with proper data types
            self.daily_stats_df = pd.read_csv(
                self.daily_stats_csv_path,
                dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
                parse_dates=["date"],
            )

            logger.info(f"Loaded {len(self.daily_stats_df):,} daily stats records")

            # Pre-index by vehicle_id for fast lookup
            logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
            self.daily_stats_by_vehicle = {}

            for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
                # Sort by date for each vehicle
                vehicle_data = group.sort_values("date").copy()
                self.daily_stats_by_vehicle[vehicle_id] = vehicle_data

            logger.info(
                f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
            )

            total_vehicles_with_data = len(self.daily_stats_by_vehicle)
            logger.info(
                f"Daily stats CSV loaded successfully: {total_vehicles_with_data:,} vehicles with activity data"
            )

        except Exception as e:
            logger.error(f"Failed to load daily stats CSV: {e}")
            raise Exception(f"Could not load daily stats CSV file: {e}")

    def get_latest_daily_stats_date(self, vehicle_id: int) -> Optional[pd.Timestamp]:
        """
        Get the latest daily stats date for a given vehicle_id using CSV data.

        Args:
            vehicle_id: The vehicle ID to query

        Returns:
            Latest date from daily stats, or None if no data exists
        """
        try:
            # Get vehicle data from pre-indexed CSV data
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.debug(f"No daily stats found for vehicle_id {vehicle_id}")
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if vehicle_data.empty:
                logger.debug(f"Empty daily stats data for vehicle_id {vehicle_id}")
                return None

            # Get the latest date
            latest_date = vehicle_data["date"].max()

            if pd.notna(latest_date):
                logger.debug(
                    f"Latest daily stats date for vehicle_id {vehicle_id}: {latest_date}"
                )
                return latest_date
            else:
                logger.debug(f"No valid dates found for vehicle_id {vehicle_id}")
                return None

        except Exception as e:
            logger.error(
                f"Error querying daily stats for vehicle_id {vehicle_id}: {str(e)}"
            )
            return None

    def get_latest_daily_stats_dates_batch(
        self, vehicle_ids: List[int]
    ) -> Dict[int, Optional[pd.Timestamp]]:
        """
        Get latest daily stats dates for multiple vehicle_ids using CSV data.

        Args:
            vehicle_ids: List of vehicle IDs to query

        Returns:
            Dictionary mapping vehicle_id to latest date (or None if no data)
        """
        if not vehicle_ids:
            logger.warning("No vehicle_ids provided for batch daily stats query")
            return {}

        try:
            logger.info(
                f"Querying latest daily stats dates for {len(vehicle_ids)} vehicles using CSV data"
            )

            date_mappings = {}

            for vehicle_id in vehicle_ids:
                latest_date = self.get_latest_daily_stats_date(vehicle_id)
                date_mappings[vehicle_id] = latest_date

            found_count = sum(1 for date in date_mappings.values() if date is not None)
            logger.info(
                f"Found daily stats for {found_count}/{len(vehicle_ids)} vehicles"
            )

            return date_mappings

        except Exception as e:
            logger.error(f"Error during batch daily stats query: {str(e)}")
            # Return None for all vehicle_ids on error
            return {vid: None for vid in vehicle_ids}

    def get_latest_daily_stats_for_vin(self, vin: str) -> Optional[pd.Timestamp]:
        """
        Get latest daily stats date for a VIN (combines VIN mapping and daily stats query).

        Args:
            vin: VIN to query

        Returns:
            Latest daily stats date, or None if VIN not found or no daily stats
        """
        try:
            # Get vehicle_id for VIN
            vehicle_id = self.get_vehicle_id_for_vin(vin)
            if vehicle_id is None:
                logger.debug(
                    f"Cannot get daily stats for VIN {vin} - vehicle_id not found"
                )
                return None

            # Get latest daily stats date
            return self.get_latest_daily_stats_date(vehicle_id)

        except Exception as e:
            logger.error(f"Error getting daily stats for VIN {vin}: {str(e)}")
            return None

    def get_latest_daily_stats_for_vins_batch(
        self, vins: List[str]
    ) -> Dict[str, Optional[pd.Timestamp]]:
        """
        Get latest daily stats dates for multiple VINs in batch.

        Args:
            vins: List of VINs to query

        Returns:
            Dictionary mapping VIN to latest daily stats date (or None)
        """
        try:
            if not vins:
                return {}

            logger.info(f"Getting latest daily stats for {len(vins)} VINs")

            # First, get VIN to vehicle_id mappings
            vin_mappings = self.load_vin_mappings(vins)

            # Extract vehicle_ids that were successfully mapped
            vehicle_ids = [vid for vid in vin_mappings.values() if vid is not None]

            if not vehicle_ids:
                logger.warning("No vehicle_ids found for provided VINs")
                return {vin: None for vin in vins}

            # Get daily stats dates for vehicle_ids
            vehicle_date_mappings = self.get_latest_daily_stats_dates_batch(vehicle_ids)

            # Map back to VINs
            vin_date_mappings = {}
            for vin in vins:
                vehicle_id = vin_mappings.get(vin)
                if vehicle_id is not None:
                    vin_date_mappings[vin] = vehicle_date_mappings.get(vehicle_id)
                else:
                    vin_date_mappings[vin] = None

            found_count = sum(
                1 for date in vin_date_mappings.values() if date is not None
            )
            logger.info(f"Found daily stats for {found_count}/{len(vins)} VINs")

            return vin_date_mappings

        except Exception as e:
            logger.error(f"Error getting batch daily stats for VINs: {str(e)}")
            return {vin: None for vin in vins}


class BatteryGrouper:
    """
    Handles grouping and aggregation of battery timeline records.

    This class groups timeline records by battery_id and finds the earliest start_date
    and latest end_date across all periods for each battery. This is essential for
    calculating accurate battery ages when batteries are transferred between vehicles.

    Usage Examples:
        Basic grouping:
            >>> timeline_data = pd.read_csv("battery_timeline.csv")
            >>> grouper = BatteryGrouper(timeline_data)
            >>> grouped = grouper.group_battery_timelines()
            >>> print(f"Grouped {len(grouped)} unique batteries")

        Analyzing grouped data:
            >>> for battery_id, info in grouped.items():
            ...     periods = info['period_count']
            ...     start = info['earliest_start']
            ...     end = info['latest_end']
            ...     print(f"Battery {battery_id}: {periods} periods, {start} to {end}")

        Finding batteries with missing dates:
            >>> missing = grouper.get_batteries_with_missing_dates()
            >>> print(f"Missing start dates: {len(missing['missing_start'])}")
            >>> print(f"Missing end dates: {len(missing['missing_end'])}")

        Getting specific battery info:
            >>> battery_info = grouper.get_battery_aggregation("BATT001")
            >>> if battery_info:
            ...     print(f"Battery has {battery_info['period_count']} periods")

    Attributes:
        timeline_data (pd.DataFrame): Input timeline data with battery records
        grouped_batteries (Dict[str, Dict[str, Any]]): Grouped battery information
    """

    def __init__(self, timeline_data: pd.DataFrame):
        """
        Initialize the BatteryGrouper.

        Args:
            timeline_data: DataFrame containing battery timeline records
        """
        self.timeline_data = timeline_data
        self.grouped_batteries: Dict[str, Dict[str, Any]] = {}

    def group_battery_timelines(self) -> Dict[str, Dict[str, Any]]:
        """
        Group timeline records by battery_id and aggregate date ranges.

        Returns:
            Dictionary mapping battery_id to aggregated timeline information:
            {
                'battery_id': {
                    'records': DataFrame of all records for this battery,
                    'earliest_start': earliest start_date across all periods,
                    'latest_end': latest end_date across all periods,
                    'period_count': number of timeline periods
                }
            }
        """
        logger.info("Grouping battery timeline records by battery_id")

        # Group by battery_id
        battery_groups = self.timeline_data.groupby("battery_id")

        for battery_id, group in battery_groups:
            try:
                # Find earliest start_date across all periods for this battery
                earliest_start = group["start_date"].min()

                # Find latest end_date across all periods for this battery
                latest_end = group["end_date"].max()

                # Store aggregated information
                self.grouped_batteries[battery_id] = {
                    "records": group.copy(),
                    "earliest_start": earliest_start,
                    "latest_end": latest_end,
                    "period_count": len(group),
                }

                logger.debug(
                    f"Battery {battery_id}: {len(group)} periods, "
                    f"earliest_start={earliest_start}, latest_end={latest_end}"
                )

            except Exception as e:
                logger.warning(f"Error grouping battery {battery_id}: {str(e)}")
                # Still store the group with error information
                self.grouped_batteries[battery_id] = {
                    "records": group.copy(),
                    "earliest_start": None,
                    "latest_end": None,
                    "period_count": len(group),
                    "error": str(e),
                }

        logger.info(f"Successfully grouped {len(self.grouped_batteries)} batteries")
        return self.grouped_batteries

    def get_battery_aggregation(self, battery_id: str) -> Optional[Dict[str, Any]]:
        """
        Get aggregated timeline information for a specific battery.

        Args:
            battery_id: The battery ID to retrieve

        Returns:
            Dictionary with aggregated timeline info, or None if not found
        """
        return self.grouped_batteries.get(battery_id)

    def get_batteries_with_missing_dates(self) -> Dict[str, List[str]]:
        """
        Identify batteries with missing start or end dates.

        Returns:
            Dictionary with 'missing_start' and 'missing_end' keys containing
            lists of battery_ids with missing dates
        """
        missing_dates = {"missing_start": [], "missing_end": []}

        for battery_id, info in self.grouped_batteries.items():
            if pd.isna(info["earliest_start"]):
                missing_dates["missing_start"].append(battery_id)
            if pd.isna(info["latest_end"]):
                missing_dates["missing_end"].append(battery_id)

        logger.info(
            f"Found {len(missing_dates['missing_start'])} batteries with missing start dates"
        )
        logger.info(
            f"Found {len(missing_dates['missing_end'])} batteries with missing end dates"
        )

        return missing_dates


class AgeCalculator:
    """
    Handles battery age calculation with comprehensive error handling and validation.

    This class provides core calculation logic for determining battery age in days
    based on earliest start date and latest end date, with proper validation
    and error handling for edge cases.

    The calculator handles various scenarios:
    - Normal date ranges with valid start and end dates
    - Missing start or end dates (returns error with descriptive note)
    - Invalid date ranges where end_date < start_date
    - Same-day periods (returns 0 days)
    - Pandas NaT (Not-a-Time) values and None dates

    Usage Examples:
        Basic age calculation:
            >>> calculator = AgeCalculator()
            >>> start_date = pd.Timestamp('2023-01-01')
            >>> end_date = pd.Timestamp('2023-12-31')
            >>> age_days, note = calculator.calculate_age_days(start_date, end_date, 'BATT001')
            >>> print(f"Battery age: {age_days} days")

        With error handling:
            >>> calculator = AgeCalculator()
            >>> age_days, note = calculator.calculate_age_days(None, end_date, 'BATT002')
            >>> if age_days is None:
            ...     print(f"Error: {note}")
            ... else:
            ...     print(f"Age: {age_days} days")

        Using BatteryAge objects:
            >>> battery_age = calculator.calculate_battery_age('BATT003', start_date, end_date)
            >>> if battery_age.age_days is not None:
            ...     print(f"Battery {battery_age.battery_id}: {battery_age.age_days} days")
            ... else:
            ...     print(f"Error for {battery_age.battery_id}: {battery_age.note}")

        Getting calculation statistics:
            >>> stats = calculator.get_calculation_stats()
            >>> print(f"Successful: {stats['successful_calculations']}")
            >>> print(f"Errors: {stats['validation_errors'] + stats['missing_date_errors']}")

    Attributes:
        calculation_stats (Dict[str, int]): Statistics tracking successful calculations and errors
    """

    def __init__(self):
        """Initialize the AgeCalculator."""
        self.calculation_stats = {
            "successful_calculations": 0,
            "validation_errors": 0,
            "missing_date_errors": 0,
            "calculation_errors": 0,
        }

    def calculate_age_days(
        self,
        earliest_start: Optional[pd.Timestamp],
        latest_end: Optional[pd.Timestamp],
        battery_id: str = "",
    ) -> Tuple[Optional[float], str]:
        """
        Calculate age in days using (latest_end_date - earliest_start_date).days.

        Args:
            earliest_start: Earliest start date for the battery
            latest_end: Latest end date for the battery
            battery_id: Battery ID for logging purposes (optional)

        Returns:
            Tuple of (age_days, error_note):
            - age_days: Calculated age in days rounded to 2 decimal places, or None if error
            - error_note: Empty string if successful, error message if failed
        """
        try:
            # Handle pandas NaT values and None dates gracefully
            if pd.isna(earliest_start) or earliest_start is None:
                self.calculation_stats["missing_date_errors"] += 1
                error_msg = "Missing start date - cannot calculate age"
                logger.debug(f"Battery {battery_id}: {error_msg}")
                return None, error_msg

            if pd.isna(latest_end) or latest_end is None:
                self.calculation_stats["missing_date_errors"] += 1
                error_msg = "Missing end date - cannot calculate age"
                logger.debug(f"Battery {battery_id}: {error_msg}")
                return None, error_msg

            # Additional validation for invalid timestamp types
            try:
                # Ensure we can work with the timestamps
                if not isinstance(earliest_start, (pd.Timestamp, datetime, date)):
                    self.calculation_stats["validation_errors"] += 1
                    error_msg = f"Invalid start date type: {type(earliest_start)}"
                    logger.warning(f"Battery {battery_id}: {error_msg}")
                    return None, error_msg

                if not isinstance(latest_end, (pd.Timestamp, datetime, date)):
                    self.calculation_stats["validation_errors"] += 1
                    error_msg = f"Invalid end date type: {type(latest_end)}"
                    logger.warning(f"Battery {battery_id}: {error_msg}")
                    return None, error_msg

            except Exception as type_error:
                self.calculation_stats["validation_errors"] += 1
                error_msg = f"Date type validation error: {str(type_error)}"
                logger.warning(f"Battery {battery_id}: {error_msg}")
                return None, error_msg

            # Validate that end_date is after start_date
            if latest_end < earliest_start:
                self.calculation_stats["validation_errors"] += 1
                error_msg = f"Invalid date range: end date ({latest_end.date()}) before start date ({earliest_start.date()})"
                logger.warning(f"Battery {battery_id}: {error_msg}")
                return None, error_msg

            # Handle edge case of same-day periods
            if latest_end == earliest_start:
                logger.debug(f"Battery {battery_id}: Same-day period detected")
                age_days = 0.0
            else:
                # Calculate age in days
                try:
                    age_days = (latest_end - earliest_start).days

                    # Convert to float for consistency and validate result
                    age_days = float(age_days)

                    # Validate reasonable age range (not negative, not excessively large)
                    if age_days < 0:
                        self.calculation_stats["validation_errors"] += 1
                        error_msg = f"Calculated negative age: {age_days} days"
                        logger.warning(f"Battery {battery_id}: {error_msg}")
                        return None, error_msg

                    # Check for unreasonably large ages (more than 50 years)
                    if age_days > 18250:  # ~50 years
                        logger.warning(
                            f"Battery {battery_id}: Very large age calculated: {age_days} days"
                        )

                except (OverflowError, ValueError) as calc_error:
                    self.calculation_stats["calculation_errors"] += 1
                    error_msg = f"Date calculation overflow: {str(calc_error)}"
                    logger.error(f"Battery {battery_id}: {error_msg}")
                    return None, error_msg

            # Round results to 2 decimal places for readability
            age_days = round(age_days, 2)

            self.calculation_stats["successful_calculations"] += 1
            logger.debug(f"Battery {battery_id}: Calculated age = {age_days} days")

            return age_days, ""

        except Exception as e:
            self.calculation_stats["calculation_errors"] += 1
            error_msg = f"Unexpected calculation error: {str(e)}"
            logger.error(f"Battery {battery_id}: {error_msg}")
            return None, error_msg

    def calculate_battery_age(
        self,
        battery_id: str,
        earliest_start: Optional[pd.Timestamp],
        latest_end: Optional[pd.Timestamp],
    ) -> BatteryAge:
        """
        Calculate complete battery age information with error handling.

        Args:
            battery_id: The battery ID being processed
            earliest_start: Earliest start date for the battery
            latest_end: Latest end date for the battery

        Returns:
            BatteryAge object with calculated age or error information
        """
        try:
            age_days, error_note = self.calculate_age_days(
                earliest_start, latest_end, battery_id
            )

            return BatteryAge(
                battery_id=battery_id,
                earliest_start=earliest_start,
                latest_end=latest_end,
                age_days=age_days,
                note=error_note,
            )

        except Exception as e:
            self.calculation_stats["calculation_errors"] += 1
            error_msg = f"Unexpected calculation error: {str(e)}"
            logger.error(f"Battery {battery_id}: {error_msg}")

            return BatteryAge(
                battery_id=battery_id,
                earliest_start=earliest_start,
                latest_end=latest_end,
                age_days=None,
                note=error_msg,
            )

    def validate_dates(
        self, start_date: Optional[pd.Timestamp], end_date: Optional[pd.Timestamp]
    ) -> Tuple[bool, str]:
        """
        Validate date inputs for age calculation with comprehensive error handling.

        Args:
            start_date: Start date to validate
            end_date: End date to validate

        Returns:
            Tuple of (is_valid, error_message):
            - is_valid: True if dates are valid for calculation
            - error_message: Empty string if valid, error description if invalid
        """
        try:
            # Check for None values
            if start_date is None:
                return False, "Start date is None"

            if end_date is None:
                return False, "End date is None"

            # Handle pandas NaT values gracefully
            if pd.isna(start_date):
                return False, "Start date is NaT (Not-a-Time)"

            if pd.isna(end_date):
                return False, "End date is NaT (Not-a-Time)"

            # Validate date types
            try:
                if not isinstance(start_date, (pd.Timestamp, datetime, date)):
                    return False, f"Invalid start date type: {type(start_date)}"

                if not isinstance(end_date, (pd.Timestamp, datetime, date)):
                    return False, f"Invalid end date type: {type(end_date)}"

            except Exception as type_error:
                return False, f"Date type validation failed: {str(type_error)}"

            # Validate date range - end_date should be after or equal to start_date
            try:
                if end_date < start_date:
                    return (
                        False,
                        f"End date ({end_date.date()}) is before start date ({start_date.date()})",
                    )
            except Exception as comparison_error:
                return False, f"Date comparison failed: {str(comparison_error)}"

            # Additional validation for reasonable date ranges
            try:
                age_days = (end_date - start_date).days
                if age_days < 0:
                    return False, f"Calculated negative age: {age_days} days"

                # Check for unreasonably large ages (more than 50 years)
                if age_days > 18250:  # ~50 years
                    return False, f"Unreasonably large age: {age_days} days (>50 years)"

            except (OverflowError, ValueError) as calc_error:
                return False, f"Date calculation error: {str(calc_error)}"

            return True, ""

        except Exception as e:
            return False, f"Unexpected validation error: {str(e)}"

    def get_calculation_stats(self) -> Dict[str, int]:
        """
        Get calculation statistics.

        Returns:
            Dictionary with calculation statistics
        """
        return self.calculation_stats.copy()

    def reset_stats(self) -> None:
        """Reset calculation statistics."""
        self.calculation_stats = {
            "successful_calculations": 0,
            "validation_errors": 0,
            "missing_date_errors": 0,
            "calculation_errors": 0,
        }

    def log_calculation_summary(self) -> None:
        """Log summary of calculation statistics."""
        stats = self.calculation_stats
        total_attempts = sum(stats.values())

        if total_attempts == 0:
            logger.info("No age calculations performed")
            return

        logger.info("=== Age Calculation Summary ===")
        logger.info(f"Total calculation attempts: {total_attempts}")
        logger.info(f"Successful calculations: {stats['successful_calculations']}")
        logger.info(f"Missing date errors: {stats['missing_date_errors']}")
        logger.info(f"Validation errors: {stats['validation_errors']}")
        logger.info(f"Calculation errors: {stats['calculation_errors']}")

        if total_attempts > 0:
            success_rate = (stats["successful_calculations"] / total_attempts) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")


class MissingDateHandler:
    """
    Handles missing date logic for battery timeline records.

    This class implements logic to fill missing start and end dates by using
    available dates from the same battery's other periods.
    """

    def __init__(self, grouped_batteries: Dict[str, Dict[str, Any]]):
        """
        Initialize the MissingDateHandler.

        Args:
            grouped_batteries: Dictionary from BatteryGrouper with battery timeline info
        """
        self.grouped_batteries = grouped_batteries
        self.date_fixes_applied = {
            "start_dates_fixed": 0,
            "end_dates_fixed": 0,
            "unfixable_start_dates": 0,
            "unfixable_end_dates": 0,
        }

    def handle_missing_dates(self) -> Dict[str, Dict[str, Any]]:
        """
        Handle missing start and end dates for all batteries.

        Returns:
            Updated grouped_batteries dictionary with missing dates filled where possible
        """
        logger.info("Starting missing date handling process")

        for battery_id, battery_info in self.grouped_batteries.items():
            try:
                # Handle missing start dates
                if pd.isna(battery_info["earliest_start"]):
                    fixed_start = self._handle_missing_start_date(
                        battery_id, battery_info
                    )
                    if fixed_start is not None:
                        battery_info["earliest_start"] = fixed_start
                        self.date_fixes_applied["start_dates_fixed"] += 1
                        logger.debug(
                            f"Fixed missing start date for battery {battery_id}: {fixed_start}"
                        )
                    else:
                        self.date_fixes_applied["unfixable_start_dates"] += 1
                        logger.debug(
                            f"Could not fix missing start date for battery {battery_id}"
                        )

                # Handle missing end dates
                if pd.isna(battery_info["latest_end"]):
                    fixed_end = self._handle_missing_end_date(battery_id, battery_info)
                    if fixed_end is not None:
                        battery_info["latest_end"] = fixed_end
                        self.date_fixes_applied["end_dates_fixed"] += 1
                        logger.debug(
                            f"Fixed missing end date for battery {battery_id}: {fixed_end}"
                        )
                    else:
                        self.date_fixes_applied["unfixable_end_dates"] += 1
                        logger.debug(
                            f"Could not fix missing end date for battery {battery_id}"
                        )

            except Exception as e:
                logger.warning(
                    f"Error handling missing dates for battery {battery_id}: {str(e)}"
                )

        self._log_date_fix_summary()
        return self.grouped_batteries

    def _handle_missing_start_date(
        self, battery_id: str, battery_info: Dict[str, Any]
    ) -> Optional[pd.Timestamp]:
        """
        Handle missing start date by using next available start_date for same battery.

        Args:
            battery_id: The battery ID being processed
            battery_info: Battery information dictionary

        Returns:
            Fixed start date if available, None otherwise
        """
        try:
            records = battery_info["records"]

            # Get all non-null start dates for this battery, sorted ascending
            valid_start_dates = records["start_date"].dropna().sort_values()

            if len(valid_start_dates) > 0:
                # Use the earliest available start_date (next available)
                next_available_start = valid_start_dates.iloc[0]
                logger.debug(
                    f"Using next available start_date for battery {battery_id}: {next_available_start}"
                )
                return next_available_start
            else:
                logger.debug(f"No valid start dates found for battery {battery_id}")
                return None

        except Exception as e:
            logger.warning(
                f"Error fixing start date for battery {battery_id}: {str(e)}"
            )
            return None

    def _handle_missing_end_date(
        self, battery_id: str, battery_info: Dict[str, Any]
    ) -> Optional[pd.Timestamp]:
        """
        Handle missing end date by using previous available end_date for same battery.

        Args:
            battery_id: The battery ID being processed
            battery_info: Battery information dictionary

        Returns:
            Fixed end date if available, None otherwise
        """
        try:
            records = battery_info["records"]

            # Get all non-null end dates for this battery, sorted descending
            valid_end_dates = records["end_date"].dropna().sort_values(ascending=False)

            if len(valid_end_dates) > 0:
                # Use the latest available end_date (previous available)
                previous_available_end = valid_end_dates.iloc[0]
                logger.debug(
                    f"Using previous available end_date for battery {battery_id}: {previous_available_end}"
                )
                return previous_available_end
            else:
                logger.debug(f"No valid end dates found for battery {battery_id}")
                return None

        except Exception as e:
            logger.warning(f"Error fixing end date for battery {battery_id}: {str(e)}")
            return None

    def validate_date_logic(self) -> Dict[str, List[str]]:
        """
        Validate that date logic is sound after applying fixes.

        Returns:
            Dictionary with validation results:
            {
                'invalid_ranges': [list of battery_ids with end_date < start_date],
                'valid_ranges': [list of battery_ids with valid date ranges]
            }
        """
        validation_results = {"invalid_ranges": [], "valid_ranges": []}

        for battery_id, battery_info in self.grouped_batteries.items():
            try:
                earliest_start = battery_info["earliest_start"]
                latest_end = battery_info["latest_end"]

                # Skip validation if either date is still missing
                if pd.isna(earliest_start) or pd.isna(latest_end):
                    continue

                # Check if end date is before start date
                if latest_end < earliest_start:
                    validation_results["invalid_ranges"].append(battery_id)
                    logger.warning(
                        f"Invalid date range for battery {battery_id}: "
                        f"end_date ({latest_end}) < start_date ({earliest_start})"
                    )
                else:
                    validation_results["valid_ranges"].append(battery_id)

            except Exception as e:
                logger.warning(
                    f"Error validating dates for battery {battery_id}: {str(e)}"
                )

        logger.info(
            f"Date validation complete: {len(validation_results['valid_ranges'])} valid, "
            f"{len(validation_results['invalid_ranges'])} invalid ranges"
        )

        return validation_results

    def _log_date_fix_summary(self) -> None:
        """Log summary of date fixes applied."""
        logger.info("Missing date handling summary:")
        logger.info(
            f"  Start dates fixed: {self.date_fixes_applied['start_dates_fixed']}"
        )
        logger.info(f"  End dates fixed: {self.date_fixes_applied['end_dates_fixed']}")
        logger.info(
            f"  Unfixable start dates: {self.date_fixes_applied['unfixable_start_dates']}"
        )
        logger.info(
            f"  Unfixable end dates: {self.date_fixes_applied['unfixable_end_dates']}"
        )


class ActiveBatteryProcessor:
    """
    Handles end date filling for currently active batteries.

    This class identifies batteries with is_currently_active = True and updates
    their end_date with the latest daily stats activity date using VIN to vehicle_id mapping.
    """

    def __init__(
        self,
        grouped_batteries: Dict[str, Dict[str, Any]],
        db_connector: DatabaseConnector,
    ):
        """
        Initialize the ActiveBatteryProcessor.

        Args:
            grouped_batteries: Dictionary from BatteryGrouper with battery timeline info
            db_connector: DatabaseConnector instance for VIN mapping and daily stats queries
        """
        self.grouped_batteries = grouped_batteries
        self.db_connector = db_connector
        self.processing_stats = {
            "active_batteries_found": 0,
            "end_dates_updated": 0,
            "vin_mapping_failures": 0,
            "daily_stats_not_found": 0,
            "processing_errors": 0,
        }

    def process_active_batteries(self) -> Dict[str, Dict[str, Any]]:
        """
        Process all active batteries and update their end dates with latest daily stats.

        Returns:
            Updated grouped_batteries dictionary with end dates filled for active batteries
        """
        logger.info("Starting active battery processing for end date filling")

        # Find all active batteries
        active_batteries = self._identify_active_batteries()

        if not active_batteries:
            logger.info("No active batteries found")
            return self.grouped_batteries

        logger.info(f"Found {len(active_batteries)} active batteries to process")

        # Process active batteries in batch for efficiency
        self._process_active_batteries_batch(active_batteries)

        # Log processing summary
        self._log_processing_summary()

        return self.grouped_batteries

    def _identify_active_batteries(self) -> List[Dict[str, Any]]:
        """
        Identify batteries with is_currently_active = True.

        Returns:
            List of dictionaries containing active battery information:
            [{'battery_id': str, 'vin': str, 'record': DataFrame row}, ...]
        """
        active_batteries = []

        for battery_id, battery_info in self.grouped_batteries.items():
            try:
                records = battery_info["records"]

                # Find records where is_currently_active = True
                active_records = records[records["is_currently_active"] == True]

                if len(active_records) > 0:
                    self.processing_stats["active_batteries_found"] += 1

                    # For each active record, store battery_id and VIN
                    for _, record in active_records.iterrows():
                        active_batteries.append(
                            {
                                "battery_id": battery_id,
                                "vin": record["vin"],
                                "record": record,
                            }
                        )

                        logger.debug(
                            f"Found active battery {battery_id} with VIN {record['vin']}"
                        )

            except Exception as e:
                logger.warning(
                    f"Error identifying active status for battery {battery_id}: {str(e)}"
                )
                self.processing_stats["processing_errors"] += 1

        return active_batteries

    def _process_active_batteries_batch(
        self, active_batteries: List[Dict[str, Any]]
    ) -> None:
        """
        Process active batteries in batch to update end dates efficiently.

        Args:
            active_batteries: List of active battery information dictionaries
        """
        try:
            # Extract unique VINs for batch processing
            unique_vins = list(set([battery["vin"] for battery in active_batteries]))
            logger.info(
                f"Processing {len(unique_vins)} unique VINs for active batteries"
            )

            # Get latest daily stats dates for all VINs in batch
            vin_date_mappings = self.db_connector.get_latest_daily_stats_for_vins_batch(
                unique_vins
            )

            # Update end dates for each active battery
            for battery_info in active_batteries:
                battery_id = battery_info["battery_id"]
                vin = battery_info["vin"]

                try:
                    # Get latest daily stats date for this VIN
                    latest_date = vin_date_mappings.get(vin)

                    if latest_date is not None:
                        # Update the battery's latest_end date
                        self._update_battery_end_date(battery_id, latest_date)
                        self.processing_stats["end_dates_updated"] += 1
                        logger.debug(
                            f"Updated end date for battery {battery_id} to {latest_date}"
                        )
                    else:
                        # No daily stats found - use current date as fallback
                        current_date = pd.Timestamp.now()
                        self._update_battery_end_date(battery_id, current_date)
                        self.processing_stats["daily_stats_not_found"] += 1
                        logger.debug(
                            f"No daily stats for VIN {vin}, using current date for battery {battery_id}"
                        )

                except Exception as e:
                    logger.warning(
                        f"Error processing active battery {battery_id}: {str(e)}"
                    )
                    self.processing_stats["processing_errors"] += 1

        except Exception as e:
            logger.error(f"Error during batch processing of active batteries: {str(e)}")
            self.processing_stats["processing_errors"] += len(active_batteries)

    def _update_battery_end_date(
        self, battery_id: str, new_end_date: pd.Timestamp
    ) -> None:
        """
        Update the latest_end date for a battery.

        Args:
            battery_id: The battery ID to update
            new_end_date: The new end date to set
        """
        try:
            if battery_id in self.grouped_batteries:
                battery_info = self.grouped_batteries[battery_id]

                # Only update if the new date is later than the current latest_end
                current_latest_end = battery_info.get("latest_end")

                if pd.isna(current_latest_end) or new_end_date > current_latest_end:
                    battery_info["latest_end"] = new_end_date
                    logger.debug(
                        f"Updated latest_end for battery {battery_id}: {new_end_date}"
                    )
                else:
                    logger.debug(
                        f"New date {new_end_date} not later than current latest_end {current_latest_end} for battery {battery_id}"
                    )
            else:
                logger.warning(f"Battery {battery_id} not found in grouped_batteries")

        except Exception as e:
            logger.warning(
                f"Error updating end date for battery {battery_id}: {str(e)}"
            )

    def _log_processing_summary(self) -> None:
        """Log summary of active battery processing."""
        stats = self.processing_stats
        logger.info("Active battery processing summary:")
        logger.info(f"  Active batteries found: {stats['active_batteries_found']}")
        logger.info(f"  End dates updated: {stats['end_dates_updated']}")
        logger.info(f"  VIN mapping failures: {stats['vin_mapping_failures']}")
        logger.info(f"  Daily stats not found: {stats['daily_stats_not_found']}")
        logger.info(f"  Processing errors: {stats['processing_errors']}")


class ResultExporter:
    """
    Handles CSV export functionality for battery age calculation results.

    This class generates CSV output with proper formatting, handles successful
    calculations and error cases, and uses descriptive filenames with timestamps.
    """

    def __init__(self):
        """Initialize the ResultExporter."""
        self.export_stats = {
            "total_batteries": 0,
            "successful_calculations": 0,
            "error_cases": 0,
            "export_timestamp": None,
        }

    def export_results_to_csv(
        self, battery_ages: List[BatteryAge], output_path: str
    ) -> str:
        """
        Export battery age results to CSV file.

        Args:
            battery_ages: List of BatteryAge objects with calculation results
            output_path: Path where CSV file should be saved

        Returns:
            Full path to the exported CSV file

        Raises:
            BatteryAgeCalculatorError: If export fails
        """
        try:
            logger.info(f"Exporting {len(battery_ages)} battery age results to CSV")

            # Generate descriptive filename with timestamp if needed
            final_output_path = self._generate_output_filename(output_path)

            # Prepare data for CSV export
            export_data = self._prepare_export_data(battery_ages)

            # Create DataFrame and export to CSV
            df = pd.DataFrame(export_data)
            df.to_csv(final_output_path, index=False)

            # Update export statistics
            self.export_stats["export_timestamp"] = datetime.now()

            logger.info(f"Successfully exported results to: {final_output_path}")
            self._log_export_summary()

            return final_output_path

        except Exception as e:
            logger.error(f"Failed to export results to CSV: {str(e)}")
            raise BatteryAgeCalculatorError(f"CSV export failed: {str(e)}")

    def _generate_output_filename(self, output_path: str) -> str:
        """
        Generate descriptive filename with timestamp if needed.

        Args:
            output_path: Original output path

        Returns:
            Final output path with timestamp if needed
        """
        try:
            # If output_path already has a timestamp or specific name, use it as-is
            if os.path.basename(output_path).startswith("battery_age_results_"):
                return output_path

            # Generate timestamp-based filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Handle different path formats
            if output_path.endswith(".csv"):
                # Replace filename with timestamped version
                directory = os.path.dirname(output_path)
                filename = f"battery_age_results_{timestamp}.csv"
                final_path = (
                    os.path.join(directory, filename) if directory else filename
                )
            else:
                # Treat as directory path
                filename = f"battery_age_results_{timestamp}.csv"
                final_path = os.path.join(output_path, filename)

            logger.debug(f"Generated output filename: {final_path}")
            return final_path

        except Exception as e:
            logger.warning(
                f"Error generating output filename, using original: {str(e)}"
            )
            return output_path

    def _prepare_export_data(
        self, battery_ages: List[BatteryAge]
    ) -> List[Dict[str, Any]]:
        """
        Prepare battery age data for CSV export.

        Args:
            battery_ages: List of BatteryAge objects

        Returns:
            List of dictionaries ready for DataFrame creation
        """
        export_data = []

        for battery_age in battery_ages:
            try:
                # Handle successful calculations and error cases appropriately
                if battery_age.age_days is not None:
                    # Successful calculation
                    row = {
                        "battery_id": battery_age.battery_id,
                        "age_days": round(
                            battery_age.age_days, 2
                        ),  # Round to 2 decimal places
                        "note": battery_age.note if battery_age.note else "",
                    }
                    self.export_stats["successful_calculations"] += 1
                else:
                    # Error case - populate note column with error details
                    row = {
                        "battery_id": battery_age.battery_id,
                        "age_days": None,  # Leave empty for errors
                        "note": (
                            battery_age.note if battery_age.note else "Unknown error"
                        ),
                    }
                    self.export_stats["error_cases"] += 1

                export_data.append(row)
                self.export_stats["total_batteries"] += 1

            except Exception as e:
                logger.warning(
                    f"Error preparing export data for battery {battery_age.battery_id}: {str(e)}"
                )
                # Add error row
                error_row = {
                    "battery_id": battery_age.battery_id,
                    "age_days": None,
                    "note": f"Export preparation error: {str(e)}",
                }
                export_data.append(error_row)
                self.export_stats["error_cases"] += 1
                self.export_stats["total_batteries"] += 1

        logger.info(f"Prepared {len(export_data)} rows for CSV export")
        return export_data

    def _log_export_summary(self) -> None:
        """Log summary of export results."""
        stats = self.export_stats
        logger.info("=== CSV Export Summary ===")
        logger.info(f"Total batteries exported: {stats['total_batteries']}")
        logger.info(f"Successful calculations: {stats['successful_calculations']}")
        logger.info(f"Error cases: {stats['error_cases']}")

        if stats["total_batteries"] > 0:
            success_rate = (
                stats["successful_calculations"] / stats["total_batteries"]
            ) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")

        if stats["export_timestamp"]:
            logger.info(f"Export completed at: {stats['export_timestamp']}")

    def get_export_stats(self) -> Dict[str, Any]:
        """
        Get export statistics.

        Returns:
            Dictionary with export statistics
        """
        return self.export_stats.copy()

    def reset_stats(self) -> None:
        """Reset export statistics."""
        self.export_stats = {
            "total_batteries": 0,
            "successful_calculations": 0,
            "error_cases": 0,
            "export_timestamp": None,
        }


class BatteryAgeCalculator:
    """
    Main class for calculating battery ages from timeline data.

    This class orchestrates the entire battery age calculation process:
    1. Loads battery timeline data from CSV
    2. Establishes database connection for VIN mapping and daily stats
    3. Groups battery periods and calculates ages
    4. Fills missing end dates for active batteries
    5. Exports results to CSV with error handling

    The calculator handles complex scenarios including:
    - Multiple timeline periods per battery (battery transfers)
    - Missing start/end dates with intelligent fallback logic
    - Active battery end date filling using latest daily stats
    - Comprehensive error handling and validation

    Usage Examples:
        Basic usage without database:
            >>> calculator = BatteryAgeCalculator("timeline.csv", "daily_stats.csv")
            >>> results = calculator.calculate_battery_ages()
            >>> output_file = calculator.export_results("battery_ages.csv")
            >>> print(f"Processed {len(results)} batteries, saved to {output_file}")

        With database connection for VIN mapping:
            >>> db_conn = "postgresql://user:pass@localhost:5432/db"
            >>> calculator = BatteryAgeCalculator(
            ...     "timeline.csv",
            ...     "daily_stats.csv",
            ...     db_conn
            ... )
            >>> results = calculator.calculate_battery_ages()

        Error handling:
            >>> try:
            ...     calculator = BatteryAgeCalculator("timeline.csv", "daily_stats.csv")
            ...     results = calculator.calculate_battery_ages()
            ... except DataValidationError as e:
            ...     print(f"Data validation failed: {e}")
            ... except DatabaseConnectionError as e:
            ...     print(f"Database connection failed: {e}")

    Attributes:
        timeline_csv_path (str): Path to battery timeline CSV file
        daily_stats_csv_path (str): Path to daily stats CSV file
        db_connection_string (Optional[str]): Database connection string
        timeline_data (Optional[pd.DataFrame]): Loaded timeline data
        battery_ages (List[BatteryAge]): Calculated battery age results
        processing_stats (Dict): Statistics about the calculation process
    """

    def __init__(
        self,
        timeline_csv_path: str,
        daily_stats_csv_path: str = "daily_stats.csv",
        db_connection_string: Optional[str] = None,
    ):
        """
        Initialize the Battery Age Calculator.

        Args:
            timeline_csv_path: Path to the battery timeline CSV file
            daily_stats_csv_path: Path to daily stats CSV file (default: "daily_stats.csv")
            db_connection_string: Optional database connection string for PostgreSQL
        """
        self.timeline_csv_path = timeline_csv_path
        self.daily_stats_csv_path = daily_stats_csv_path
        self.db_connection_string = db_connection_string

        # Data storage
        self.timeline_data: Optional[pd.DataFrame] = None
        self.battery_ages: List[BatteryAge] = []
        self.processing_stats = {
            "total_batteries": 0,
            "successful_calculations": 0,
            "errors": 0,
            "active_batteries_processed": 0,
        }

        # Initialize components
        self.age_calculator = AgeCalculator()
        self.result_exporter = ResultExporter()

        logger.info(
            f"Initialized BatteryAgeCalculator with timeline: {timeline_csv_path}"
        )
        if daily_stats_csv_path:
            logger.info(f"Daily stats CSV: {daily_stats_csv_path}")
        if db_connection_string:
            logger.info("Database connection configured")

    def calculate_battery_ages(self) -> List[BatteryAge]:
        """
        Main method that orchestrates the entire battery age calculation process.

        This method performs the complete battery age calculation workflow:
        1. Loads and validates timeline data from CSV
        2. Groups battery periods by battery_id
        3. Handles missing dates using intelligent fallback logic
        4. Fills end dates for active batteries using daily stats
        5. Calculates age in days for each battery
        6. Returns comprehensive results with error handling

        The calculation handles complex scenarios including:
        - Multiple periods per battery (battery transfers)
        - Missing start/end dates with fallback logic
        - Active batteries requiring daily stats lookup
        - Data validation and error reporting

        Usage Examples:
            Basic calculation:
                >>> calculator = BatteryAgeCalculator("timeline.csv", "daily_stats.csv")
                >>> results = calculator.calculate_battery_ages()
                >>> successful = [r for r in results if r.age_days is not None]
                >>> print(f"Successfully calculated {len(successful)} battery ages")

            With error handling:
                >>> try:
                ...     results = calculator.calculate_battery_ages()
                ...     for result in results:
                ...         if result.age_days is not None:
                ...             print(f"Battery {result.battery_id}: {result.age_days:.1f} days")
                ...         else:
                ...             print(f"Battery {result.battery_id}: Error - {result.note}")
                ... except DataValidationError as e:
                ...     print(f"Data validation failed: {e}")

        Returns:
            List of BatteryAge objects with calculated ages and error notes.
            Each BatteryAge contains:
            - battery_id: Unique battery identifier
            - earliest_start: Earliest start date across all periods
            - latest_end: Latest end date across all periods
            - age_days: Calculated age in days (None if error)
            - note: Error description if calculation failed, empty if successful

        Raises:
            BatteryAgeCalculatorError: If critical errors occur during processing
            DataValidationError: If input data validation fails
            DatabaseConnectionError: If database operations fail (when using DB)
        """
        try:
            logger.info("Starting battery age calculation process")

            # Reset processing stats
            self.processing_stats = {
                "total_batteries": 0,
                "successful_calculations": 0,
                "errors": 0,
                "active_batteries_processed": 0,
            }

            # Load and validate timeline data
            self._load_timeline_data()

            # Process battery ages
            self._process_battery_ages()

            # Log summary statistics
            self._log_processing_summary()

            logger.info("Battery age calculation completed successfully")
            return self.battery_ages

        except Exception as e:
            logger.error(f"Critical error during battery age calculation: {str(e)}")
            raise BatteryAgeCalculatorError(
                f"Failed to calculate battery ages: {str(e)}"
            )

    def export_results(self, output_path: str) -> str:
        """
        Export battery age results to CSV file using ResultExporter.

        This method exports the calculated battery age results to a CSV file with
        proper formatting and error handling. The output CSV contains columns:
        - battery_id: Unique battery identifier
        - age_days: Calculated age in days (rounded to 2 decimal places)
        - note: Error description if calculation failed, empty if successful

        Usage Examples:
            Basic export:
                >>> calculator = BatteryAgeCalculator("timeline.csv", "daily_stats.csv")
                >>> results = calculator.calculate_battery_ages()
                >>> output_file = calculator.export_results("battery_ages.csv")
                >>> print(f"Results saved to: {output_file}")

            Export with timestamp:
                >>> output_file = calculator.export_results("results/battery_ages.csv")
                >>> # File will be saved as "results/battery_age_results_20240315_143022.csv"

            Analyzing exported results:
                >>> import pandas as pd
                >>> output_file = calculator.export_results("battery_ages.csv")
                >>> df = pd.read_csv(output_file)
                >>> successful = df[df['age_days'].notna()]
                >>> errors = df[df['age_days'].isna()]
                >>> print(f"Successful: {len(successful)}, Errors: {len(errors)}")

        Args:
            output_path: Path where the results CSV should be saved. Can be:
                - Full path with filename: "/path/to/battery_ages.csv"
                - Directory path: "/path/to/results/" (filename auto-generated)
                - Relative path: "battery_ages.csv"
                If the filename doesn't include a timestamp, one will be added automatically.

        Returns:
            Full path to the exported CSV file (may include auto-generated timestamp)

        Raises:
            BatteryAgeCalculatorError: If export fails due to file system issues
            PermissionError: If insufficient permissions to write to output path
            OSError: If disk space or file system issues occur
        """
        try:
            if not self.battery_ages:
                raise BatteryAgeCalculatorError("No battery age data to export")

            # Use ResultExporter for CSV export
            exported_file_path = self.result_exporter.export_results_to_csv(
                self.battery_ages, output_path
            )

            logger.info(
                f"Battery age results successfully exported to: {exported_file_path}"
            )
            return exported_file_path

        except Exception as e:
            logger.error(f"Failed to export results: {str(e)}")
            raise BatteryAgeCalculatorError(f"Export failed: {str(e)}")

    def _load_timeline_data(self) -> None:
        """Load and validate battery timeline data from CSV using TimelineLoader."""
        try:
            timeline_loader = TimelineLoader(self.timeline_csv_path)
            self.timeline_data = timeline_loader.load_timeline_data()

        except DataValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to load timeline data: {str(e)}")
            raise DataValidationError(f"Timeline data loading failed: {str(e)}")

    def _process_battery_ages(self) -> None:
        """Process battery timeline data to calculate ages with comprehensive missing date handling."""
        if self.timeline_data is None:
            raise BatteryAgeCalculatorError("Timeline data not loaded")

        logger.info(
            "Starting comprehensive battery age processing with missing date handling"
        )

        # Step 1: Group batteries using BatteryGrouper
        logger.info("Step 1: Grouping battery timeline records by battery_id")
        battery_grouper = BatteryGrouper(self.timeline_data)
        grouped_batteries = battery_grouper.group_battery_timelines()

        self.processing_stats["total_batteries"] = len(grouped_batteries)
        logger.info(
            f"Grouped {self.processing_stats['total_batteries']:,} unique batteries"
        )

        # Step 2: Handle missing dates using MissingDateHandler
        logger.info("Step 2: Handling missing dates using fallback logic")
        missing_date_handler = MissingDateHandler(grouped_batteries)
        grouped_batteries = missing_date_handler.handle_missing_dates()

        # Step 3: Setup database connector and load daily stats for active battery processing
        db_connector = None
        if self.db_connection_string:
            try:
                logger.info(
                    "Step 3: Setting up database connection and daily stats for active battery processing"
                )
                db_connector = DatabaseConnector(
                    connection_string=self.db_connection_string,
                    daily_stats_csv_path=self.daily_stats_csv_path,
                )

                # Load daily stats CSV data
                db_connector.load_daily_stats_csv()

                # Test database connection
                if not db_connector.connect():
                    logger.warning(
                        "Database connection failed, proceeding without VIN mapping"
                    )
                    db_connector = None
                else:
                    logger.info(
                        "Database connection successful, proceeding with active battery processing"
                    )

            except Exception as e:
                logger.warning(f"Error setting up database connector: {str(e)}")
                logger.warning("Proceeding without database connectivity")
                db_connector = None

        # Step 4: Process active batteries to fill missing end dates
        if db_connector:
            logger.info("Step 4: Processing active batteries for end date filling")
            active_battery_processor = ActiveBatteryProcessor(
                grouped_batteries, db_connector
            )
            grouped_batteries = active_battery_processor.process_active_batteries()
            self.processing_stats["active_batteries_processed"] = (
                active_battery_processor.processing_stats.get("end_dates_updated", 0)
            )
        else:
            logger.warning(
                "Step 4: Skipping active battery processing due to missing database connection"
            )
            # For active batteries without database, use current date as fallback
            current_date = pd.Timestamp.now()
            for battery_id, battery_info in grouped_batteries.items():
                records = battery_info["records"]
                active_records = records[records["is_currently_active"] == True]

                if len(active_records) > 0 and pd.isna(battery_info["latest_end"]):
                    battery_info["latest_end"] = current_date
                    logger.debug(
                        f"Used current date as fallback for active battery {battery_id}"
                    )

        # Step 5: Calculate ages using processed data
        logger.info("Step 5: Calculating battery ages using processed timeline data")

        processed_count = 0
        progress_interval = max(1, self.processing_stats["total_batteries"] // 10)

        for battery_id, battery_info in grouped_batteries.items():
            try:
                # Extract processed dates from grouped data
                earliest_start = battery_info["earliest_start"]
                latest_end = battery_info["latest_end"]

                # Use AgeCalculator for the actual calculation and validation
                battery_age = self.age_calculator.calculate_battery_age(
                    battery_id, earliest_start, latest_end
                )

                self.battery_ages.append(battery_age)

                if battery_age.age_days is not None:
                    self.processing_stats["successful_calculations"] += 1
                else:
                    self.processing_stats["errors"] += 1

                processed_count += 1

                # Log progress at intervals
                if (
                    processed_count % progress_interval == 0
                    or processed_count == self.processing_stats["total_batteries"]
                ):
                    self._log_progress_update(processed_count)

            except Exception as e:
                logger.warning(f"Error processing battery {battery_id}: {str(e)}")
                error_battery = BatteryAge(
                    battery_id=battery_id,
                    earliest_start=None,
                    latest_end=None,
                    age_days=None,
                    note=f"Processing error: {str(e)}",
                )
                self.battery_ages.append(error_battery)
                self.processing_stats["errors"] += 1
                processed_count += 1

        # Clean up database connection
        if db_connector:
            try:
                db_connector.disconnect()
            except Exception as e:
                logger.warning(f"Error disconnecting from database: {str(e)}")

        logger.info("Battery age processing completed successfully")

    def _log_progress_update(self, processed_count: int) -> None:
        """Log progress update during battery processing."""
        total = self.processing_stats["total_batteries"]
        successful = self.processing_stats["successful_calculations"]
        errors = self.processing_stats["errors"]

        progress_pct = (processed_count / total) * 100 if total > 0 else 0
        success_rate = (
            (successful / processed_count) * 100 if processed_count > 0 else 0
        )

        logger.info(
            f"Progress: {processed_count:,}/{total:,} ({progress_pct:.1f}%) - "
            f"Success: {successful:,} ({success_rate:.1f}%), Errors: {errors:,}"
        )

        # Log data quality metrics during processing if significant errors
        if errors > 0 and (errors / processed_count) > 0.1:  # More than 10% error rate
            logger.warning(
                f"High error rate detected: {errors}/{processed_count} ({errors/processed_count*100:.1f}%)"
            )

            # Sample some error cases for analysis
            error_batteries = [ba for ba in self.battery_ages if ba.age_days is None]
            if error_batteries:
                sample_errors = error_batteries[:3]  # Show first 3 errors
                logger.warning("Sample error cases:")
                for error_battery in sample_errors:
                    logger.warning(
                        f"  Battery {error_battery.battery_id}: {error_battery.note}"
                    )

    def log_processing_statistics_during_execution(self) -> None:
        """Log current processing statistics during execution."""
        stats = self.processing_stats
        calc_stats = (
            self.age_calculator.get_calculation_stats()
            if hasattr(self, "age_calculator")
            else {}
        )

        logger.info("--- Current Processing Statistics ---")
        logger.info(f"Batteries processed so far: {len(self.battery_ages):,}")
        logger.info(f"Successful calculations: {stats['successful_calculations']:,}")
        logger.info(f"Errors encountered: {stats['errors']:,}")

        if len(self.battery_ages) > 0:
            success_rate = (
                stats["successful_calculations"] / len(self.battery_ages)
            ) * 100
            logger.info(f"Current success rate: {success_rate:.1f}%")

        # Log calculation-specific statistics
        if calc_stats:
            logger.info(
                f"Missing date errors: {calc_stats.get('missing_date_errors', 0):,}"
            )
            logger.info(
                f"Validation errors: {calc_stats.get('validation_errors', 0):,}"
            )
            logger.info(
                f"Calculation errors: {calc_stats.get('calculation_errors', 0):,}"
            )

        logger.info("--- End Current Statistics ---")

    def _log_processing_summary(self) -> None:
        """Log comprehensive summary statistics of the processing results."""
        stats = self.processing_stats

        logger.info("=" * 50)
        logger.info("BATTERY AGE CALCULATION SUMMARY")
        logger.info("=" * 50)

        # Basic processing statistics
        logger.info(f"Total batteries processed: {stats['total_batteries']:,}")
        logger.info(f"Successful calculations: {stats['successful_calculations']:,}")
        logger.info(f"Errors encountered: {stats['errors']:,}")

        if stats["total_batteries"] > 0:
            success_rate = (
                stats["successful_calculations"] / stats["total_batteries"]
            ) * 100
            error_rate = (stats["errors"] / stats["total_batteries"]) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")
            logger.info(f"Error rate: {error_rate:.1f}%")

        # Data quality metrics
        self._log_data_quality_metrics()

        # Age calculation statistics from AgeCalculator
        if hasattr(self, "age_calculator") and self.age_calculator:
            calc_stats = self.age_calculator.get_calculation_stats()
            logger.info("\n--- Age Calculation Details ---")
            logger.info(
                f"Missing date errors: {calc_stats.get('missing_date_errors', 0):,}"
            )
            logger.info(
                f"Validation errors: {calc_stats.get('validation_errors', 0):,}"
            )
            logger.info(
                f"Calculation errors: {calc_stats.get('calculation_errors', 0):,}"
            )

        # Export statistics from ResultExporter
        if hasattr(self, "result_exporter") and self.result_exporter:
            export_stats = self.result_exporter.get_export_stats()
            if export_stats.get("total_batteries", 0) > 0:
                logger.info("\n--- Export Statistics ---")
                logger.info(
                    f"Total batteries exported: {export_stats.get('total_batteries', 0):,}"
                )
                logger.info(
                    f"Successful exports: {export_stats.get('successful_calculations', 0):,}"
                )
                logger.info(
                    f"Error cases exported: {export_stats.get('error_cases', 0):,}"
                )

        logger.info("=" * 50)

    def _log_data_quality_metrics(self) -> None:
        """Log data quality metrics for the processed timeline data."""
        if self.timeline_data is None:
            logger.info("No timeline data available for quality metrics")
            return

        try:
            logger.info("\n--- Data Quality Metrics ---")

            # Basic data metrics
            total_records = len(self.timeline_data)
            unique_batteries = self.timeline_data["battery_id"].nunique()
            unique_vins = self.timeline_data["vin"].nunique()

            logger.info(f"Total timeline records: {total_records:,}")
            logger.info(f"Unique batteries: {unique_batteries:,}")
            logger.info(f"Unique VINs: {unique_vins:,}")
            logger.info(
                f"Average records per battery: {total_records / unique_batteries:.1f}"
            )

            # Date quality metrics
            missing_start_dates = self.timeline_data["start_date"].isna().sum()
            missing_end_dates = self.timeline_data["end_date"].isna().sum()

            logger.info(
                f"Missing start dates: {missing_start_dates:,} ({missing_start_dates/total_records*100:.1f}%)"
            )
            logger.info(
                f"Missing end dates: {missing_end_dates:,} ({missing_end_dates/total_records*100:.1f}%)"
            )

            # Active battery metrics
            if "is_currently_active" in self.timeline_data.columns:
                active_batteries = self.timeline_data["is_currently_active"].sum()
                logger.info(f"Currently active batteries: {active_batteries:,}")

            # Date range analysis
            if not self.timeline_data["start_date"].isna().all():
                earliest_start = self.timeline_data["start_date"].min()
                latest_start = self.timeline_data["start_date"].max()
                logger.info(
                    f"Date range (start): {earliest_start.date()} to {latest_start.date()}"
                )

            if not self.timeline_data["end_date"].isna().all():
                earliest_end = self.timeline_data["end_date"].min()
                latest_end = self.timeline_data["end_date"].max()
                logger.info(
                    f"Date range (end): {earliest_end.date()} to {latest_end.date()}"
                )

        except Exception as e:
            logger.warning(f"Error calculating data quality metrics: {str(e)}")

    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive processing statistics.

        Returns:
            Dictionary with detailed processing statistics
        """
        stats = {
            "processing": self.processing_stats.copy(),
            "age_calculation": {},
            "export": {},
            "data_quality": {},
        }

        # Add age calculation statistics
        if hasattr(self, "age_calculator") and self.age_calculator:
            stats["age_calculation"] = self.age_calculator.get_calculation_stats()

        # Add export statistics
        if hasattr(self, "result_exporter") and self.result_exporter:
            stats["export"] = self.result_exporter.get_export_stats()

        # Add data quality metrics
        if self.timeline_data is not None:
            try:
                stats["data_quality"] = {
                    "total_records": len(self.timeline_data),
                    "unique_batteries": self.timeline_data["battery_id"].nunique(),
                    "unique_vins": self.timeline_data["vin"].nunique(),
                    "missing_start_dates": self.timeline_data["start_date"]
                    .isna()
                    .sum(),
                    "missing_end_dates": self.timeline_data["end_date"].isna().sum(),
                    "active_batteries": (
                        self.timeline_data["is_currently_active"].sum()
                        if "is_currently_active" in self.timeline_data.columns
                        else 0
                    ),
                }
            except Exception as e:
                logger.warning(f"Error collecting data quality metrics: {str(e)}")
                stats["data_quality"] = {"error": str(e)}

        return stats


def _get_default_db_connection() -> str:
    """Get default database connection string from environment variables."""
    host = os.getenv("DB_HOST", "localhost")
    port = os.getenv("DB_PORT", "6543")
    database = os.getenv("DB_NAME", "LeitwartenDB")
    user = os.getenv("DB_USER", "datadump")
    password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")

    return f"postgresql://{user}:{password}@{host}:{port}/{database}"


def main():
    """
    Main entry point for the battery age calculator script.

    Uses hardcoded paths for simplicity:
    - Timeline: battery_timeline_results/battery_timeline.csv
    - Daily stats: daily_stats.csv
    - Output: battery_age_results.csv
    - Database: Uses environment variables for connection
    """
    import sys
    from pathlib import Path

    # Configure logging for informative output
    logging.getLogger().setLevel(logging.INFO)

    # Define hardcoded paths
    base_dir = Path(
        "/home/<USER>/Documents/code-repository/streetscooter/battery-km-stand"
    )
    timeline_csv_path = "battery_timeline_results/battery_timeline.csv"
    daily_stats_csv_path = "daily_stats.csv"
    output_csv_path = "battery_age_results.csv"

    # Get database connection string from environment variables
    db_connection_string = _get_default_db_connection()

    # Log execution parameters
    logger.info("Battery Age Calculator - Starting execution")
    logger.info(f"Timeline CSV: {timeline_csv_path}")
    logger.info(f"Daily Stats CSV: {daily_stats_csv_path}")
    logger.info(f"Output CSV: {output_csv_path}")
    logger.info(
        f"Database connection configured: {'Yes' if db_connection_string else 'No'}"
    )

    try:
        # Show progress indicator for initialization
        logger.info("⏳ Initializing battery age calculator...")

        # Initialize the battery age calculator with comprehensive error handling
        try:
            calculator = BatteryAgeCalculator(
                timeline_csv_path=str(timeline_csv_path),
                daily_stats_csv_path=str(daily_stats_csv_path),
                db_connection_string=db_connection_string,
            )
            logger.info("✓ Calculator initialized successfully")
        except FileNotFoundError as e:
            logger.error(f"✗ File not found error: {str(e)}")
            logger.error("Please check that all input files exist and are accessible")
            sys.exit(1)
        except PermissionError as e:
            logger.error(f"✗ Permission error: {str(e)}")
            logger.error("Please check file permissions for input files")
            sys.exit(1)
        except DataValidationError as e:
            logger.error(f"✗ Data validation error: {str(e)}")
            logger.error("Please check the format and content of your input CSV files")
            sys.exit(1)
        except DatabaseConnectionError as e:
            logger.error(f"✗ Database connection error: {str(e)}")
            logger.error("Please check your database connection settings:")
            logger.error(f"  - Host: {os.getenv('DB_HOST', 'localhost')}")
            logger.error(f"  - Port: {os.getenv('DB_PORT', '6543')}")
            logger.error(f"  - Database: {os.getenv('DB_NAME', 'LeitwartenDB')}")
            logger.error(f"  - Username: {os.getenv('DB_USER', 'datadump')}")
            logger.error("  - Password: [check DB_PASSWORD environment variable]")
            logger.error("You can test the connection with: --test-db")
            sys.exit(1)
        except Exception as e:
            logger.error(f"✗ Initialization error: {str(e)}")
            logger.error("An unexpected error occurred during initialization")
            sys.exit(1)

        # Show progress indicator for calculation
        logger.info("⏳ Calculating battery ages...")
        logger.info("This may take a few minutes for large datasets...")

        # Calculate battery ages with progress feedback
        try:
            results = calculator.calculate_battery_ages()
            logger.info("✓ Battery age calculations completed")
        except DataValidationError as e:
            logger.error(f"✗ Data processing error: {str(e)}")
            logger.error("There was an issue processing the timeline data")
            sys.exit(1)
        except DatabaseConnectionError as e:
            logger.error(f"✗ Database error during processing: {str(e)}")
            logger.error("Database connection was lost during processing")
            logger.error(
                "This may be due to network issues or database server problems"
            )
            sys.exit(1)
        except MemoryError as e:
            logger.error(f"✗ Memory error: {str(e)}")
            logger.error("The dataset is too large to process with available memory")
            logger.error("Consider processing the data in smaller batches")
            sys.exit(1)
        except Exception as e:
            logger.error(f"✗ Calculation error: {str(e)}")
            logger.error("An unexpected error occurred during battery age calculation")
            sys.exit(1)

        # Show progress indicator for export
        logger.info("⏳ Exporting results to CSV...")

        # Export results with error handling
        try:
            calculator.export_results(str(output_csv_path))
            logger.info("✓ Results exported successfully")
        except PermissionError as e:
            logger.error(f"✗ Permission error writing output file: {str(e)}")
            logger.error(f"Please check write permissions for: {output_csv_path}")
            sys.exit(1)
        except OSError as e:
            logger.error(f"✗ File system error: {str(e)}")
            logger.error("There was an issue writing the output file")
            logger.error("Please check disk space and file system permissions")
            sys.exit(1)
        except Exception as e:
            logger.error(f"✗ Export error: {str(e)}")
            logger.error("An unexpected error occurred during result export")
            sys.exit(1)

        # Success message with summary
        logger.info("🎉 Battery age calculation completed successfully!")
        logger.info(f"📊 Results exported to: {output_csv_path}")

        # Provide summary statistics if available
        if hasattr(calculator, "processing_stats") and calculator.processing_stats:
            stats = calculator.processing_stats
            logger.info("📈 Processing Summary:")
            logger.info(
                f"  • Total batteries processed: {stats.get('total_batteries', 'N/A')}"
            )
            logger.info(
                f"  • Successful calculations: {stats.get('successful_calculations', 'N/A')}"
            )
            logger.info(f"  • Errors encountered: {stats.get('total_errors', 'N/A')}")

            if stats.get("total_errors", 0) > 0:
                logger.info(
                    "ℹ️  Check the 'note' column in the output file for error details"
                )

    except KeyboardInterrupt:
        logger.warning("⚠️  Operation cancelled by user")
        sys.exit(130)  # Standard exit code for SIGINT
    except SystemExit:
        # Re-raise SystemExit to preserve exit codes
        raise
    except Exception as e:
        logger.error(f"✗ Unexpected error: {str(e)}")
        logger.error(
            "An unexpected error occurred. Please check the log file for details."
        )
        logger.error(
            "If this problem persists, please contact support with the error details."
        )
        sys.exit(1)


if __name__ == "__main__":
    main()
