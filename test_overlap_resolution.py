#!/usr/bin/env python3
"""
Test script for overlap resolution improvements.
Tests the specific case mentioned: batteries 22405 and 22495 overlapping in WS5D16HAAJA102178
"""

import sys
import logging
from data_preparation_pipeline import BatteryTimelinePipeline

# Setup logging to see the conflict resolution messages
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_overlap_resolution():
    """Test the improved overlap resolution with the specific batteries mentioned."""

    # Test batteries that have the overlap issue
    test_batteries = ["22405", "22495", "23144", "23877"]

    print("Testing Overlap Resolution Improvements")
    print("=" * 50)
    print(f"Test batteries: {', '.join(test_batteries)}")
    print("Specific case: WS5D16HAAJA102178 with overlapping batteries 22405 and 22495")
    print()

    # File paths
    hv_repair_file = "hv_repair_2025-06-02b.csv"
    working_matching_vehicles_file = "comparison_results/working_matching_vehicles.csv"
    working_unique_vehicles_file = "comparison_results/working_unique_vehicles.csv"
    daily_stats_csv_file = "daily_stats.csv"

    # Initialize pipeline
    pipeline = BatteryTimelinePipeline(
        hv_repair_file,
        working_matching_vehicles_file,
        working_unique_vehicles_file,
        daily_stats_csv_file,
        test_batteries=test_batteries,
    )

    try:
        print("Running streamlined analysis...")
        results = pipeline.run_streamlined_analysis()

        if results["success"]:
            # Focus on the specific vehicle with overlaps
            target_vin = "WS5D16HAAJA102178"
            vehicle_periods = [
                p for p in pipeline.final_timeline if p["vin"] == target_vin
            ]

            print(f"\nResults for vehicle {target_vin}:")
            print("-" * 60)

            if vehicle_periods:
                # Sort by start date
                vehicle_periods.sort(key=lambda x: x["start_date"] or str(""))

                for period in vehicle_periods:
                    start_str = (
                        period["start_date"].strftime("%Y-%m-%d")
                        if period["start_date"]
                        else "None"
                    )

                    # Handle NaT values properly
                    if period["end_date"] is None:
                        end_str = "ongoing"
                    else:
                        try:
                            # Check if it's a NaT value
                            import pandas as pd

                            if pd.isna(period["end_date"]):
                                end_str = "ongoing"
                            else:
                                end_str = period["end_date"].strftime("%Y-%m-%d")
                        except (ValueError, AttributeError):
                            end_str = "ongoing"

                    print(f"Battery {period['battery_id']}: {start_str} to {end_str}")
                    print(
                        f"  Source: {period['source']}, Confidence: {period['confidence']}"
                    )
                    print(f"  Lifecycle: {period['lifecycle_stage']}")
                    print(f"  Note: {period.get('note', 'No note')}")
                    print()

                # Check for remaining overlaps
                overlaps_found = 0
                for i in range(len(vehicle_periods)):
                    for j in range(i + 1, len(vehicle_periods)):
                        p1, p2 = vehicle_periods[i], vehicle_periods[j]

                        # Check for overlap
                        start1 = p1["start_date"]
                        end1 = p1["end_date"]
                        start2 = p2["start_date"]
                        end2 = p2["end_date"]

                        if start1 and start2:
                            # If end dates are None, use a future date for comparison
                            from datetime import datetime

                            end1_comp = end1 if end1 else datetime(2025, 12, 31)
                            end2_comp = end2 if end2 else datetime(2025, 12, 31)

                            # Check overlap: start1 < end2 and start2 < end1
                            if start1 < end2_comp and start2 < end1_comp:
                                overlaps_found += 1
                                print(f"⚠️ REMAINING OVERLAP:")
                                print(
                                    f"  Battery {p1['battery_id']}: {start1.date()} to {end1.date() if end1 else 'ongoing'}"
                                )
                                print(
                                    f"  Battery {p2['battery_id']}: {start2.date()} to {end2.date() if end2 else 'ongoing'}"
                                )
                                print()

                if overlaps_found == 0:
                    print("✅ No overlaps found - resolution successful!")
                else:
                    print(f"❌ {overlaps_found} overlaps still remain")

            else:
                print(f"No periods found for vehicle {target_vin}")

            # Export results for inspection
            export_files = pipeline.export_results("test_overlap_results")
            print(f"\nResults exported to: test_overlap_results/")

            # Phase statistics
            if hasattr(pipeline, "phase_stats"):
                phase3_stats = pipeline.phase_stats.get("phase3", {})
                conflicts_resolved = phase3_stats.get("battery_conflicts_resolved", 0)
                print(f"\nPhase 3 Conflicts Resolved: {conflicts_resolved}")

        else:
            print(f"❌ Analysis failed: {results.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test_overlap_resolution()
