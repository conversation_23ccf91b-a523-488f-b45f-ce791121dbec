#!/usr/bin/env python3
"""
Example Usage Script for Battery Age Calculator

This script demonstrates typical usage patterns for the Battery Age Calculator,
including error handling, output interpretation, and performance considerations
for large datasets.

Author: Battery Analysis System
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd

# Add the current directory to Python path to import the calculator
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from battery_age_calculator import (
    BatteryAgeCalculator,
    BatteryAgeCalculatorError,
    DataValidationError,
    DatabaseConnectionError
)

# Configure logging for the example
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_basic_usage():
    """
    Example 1: Basic usage with sample data
    
    This example shows the most common usage pattern for calculating
    battery ages from timeline data.
    """
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Battery Age Calculation")
    print("="*60)
    
    try:
        # File paths for sample data
        timeline_csv = "days_calculate/battery_timeline.csv"
        daily_stats_csv = "daily_stats.csv"
        output_csv = "example_results/battery_ages_basic.csv"
        
        # Check if input files exist
        if not os.path.exists(timeline_csv):
            print(f"⚠️  Sample timeline file not found: {timeline_csv}")
            print("   Please ensure you have sample data available")
            return
        
        if not os.path.exists(daily_stats_csv):
            print(f"⚠️  Daily stats file not found: {daily_stats_csv}")
            print("   This example will continue without daily stats data")
            daily_stats_csv = None
        
        # Create output directory
        os.makedirs("example_results", exist_ok=True)
        
        print(f"📁 Input timeline: {timeline_csv}")
        print(f"📁 Daily stats: {daily_stats_csv or 'Not available'}")
        print(f"📁 Output: {output_csv}")
        
        # Initialize calculator
        print("\n⏳ Initializing battery age calculator...")
        calculator = BatteryAgeCalculator(
            timeline_csv_path=timeline_csv,
            daily_stats_csv_path=daily_stats_csv or "daily_stats.csv"
        )
        
        # Calculate battery ages
        print("⏳ Calculating battery ages...")
        results = calculator.calculate_battery_ages()
        
        # Export results
        print("⏳ Exporting results...")
        output_file = calculator.export_results(output_csv)
        
        # Display summary
        print(f"\n✅ Calculation completed successfully!")
        print(f"📊 Results saved to: {output_file}")
        print(f"🔢 Total batteries processed: {len(results)}")
        
        # Show sample results
        successful_results = [r for r in results if r.age_days is not None]
        error_results = [r for r in results if r.age_days is None]
        
        print(f"✅ Successful calculations: {len(successful_results)}")
        print(f"❌ Errors encountered: {len(error_results)}")
        
        if successful_results:
            print("\n📋 Sample successful results:")
            for i, result in enumerate(successful_results[:3]):
                print(f"   {i+1}. Battery {result.battery_id}: {result.age_days:.1f} days")
        
        if error_results:
            print("\n⚠️  Sample error cases:")
            for i, result in enumerate(error_results[:3]):
                print(f"   {i+1}. Battery {result.battery_id}: {result.note}")
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("   Please check that input files exist and are accessible")
    except DataValidationError as e:
        print(f"❌ Data validation error: {e}")
        print("   Please check the format and content of your CSV files")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("   Check the logs for more details")


def example_with_database():
    """
    Example 2: Usage with database connection for VIN mapping
    
    This example shows how to use the calculator with database connectivity
    for accurate VIN to vehicle_id mapping and daily stats queries.
    """
    print("\n" + "="*60)
    print("EXAMPLE 2: Battery Age Calculation with Database")
    print("="*60)
    
    try:
        # File paths
        timeline_csv = "days_calculate/battery_timeline.csv"
        daily_stats_csv = "daily_stats.csv"
        output_csv = "example_results/battery_ages_with_db.csv"
        
        # Check if input files exist
        if not os.path.exists(timeline_csv):
            print(f"⚠️  Sample timeline file not found: {timeline_csv}")
            return
        
        # Create output directory
        os.makedirs("example_results", exist_ok=True)
        
        print(f"📁 Input timeline: {timeline_csv}")
        print(f"📁 Daily stats: {daily_stats_csv}")
        print(f"📁 Output: {output_csv}")
        print("🔗 Database connection: Configured from config.py")
        
        # Initialize calculator with database connection
        print("\n⏳ Initializing calculator with database connection...")
        calculator = BatteryAgeCalculator(
            timeline_csv_path=timeline_csv,
            daily_stats_csv_path=daily_stats_csv
            # db_connection_string will be loaded from config.py
        )
        
        # Test database connectivity first
        print("🔍 Testing database connection...")
        # Note: Database connection testing is handled internally
        
        # Calculate battery ages
        print("⏳ Calculating battery ages with database support...")
        results = calculator.calculate_battery_ages()
        
        # Export results
        print("⏳ Exporting results...")
        output_file = calculator.export_results(output_csv)
        
        print(f"\n✅ Database-enhanced calculation completed!")
        print(f"📊 Results saved to: {output_file}")
        print(f"🔢 Total batteries processed: {len(results)}")
        
        # Show enhanced statistics
        active_batteries = [r for r in results if "active" in r.note.lower()]
        print(f"🔋 Active batteries processed: {len(active_batteries)}")
        
    except DatabaseConnectionError as e:
        print(f"❌ Database connection error: {e}")
        print("   Please check your database configuration:")
        print("   - Verify database server is running")
        print("   - Check connection parameters in config.py")
        print("   - Ensure network connectivity")
        print("   - Test with: python battery_age_calculator.py --test-db")
    except Exception as e:
        print(f"❌ Error: {e}")


def example_error_handling():
    """
    Example 3: Comprehensive error handling and output interpretation
    
    This example demonstrates how to handle various error conditions
    and interpret the results properly.
    """
    print("\n" + "="*60)
    print("EXAMPLE 3: Error Handling and Output Interpretation")
    print("="*60)
    
    # Example with intentionally problematic data
    try:
        # Create a sample problematic dataset
        problematic_data = {
            'battery_id': ['GOOD_BATTERY', 'MISSING_START', 'MISSING_END', 'INVALID_RANGE'],
            'vin': ['VIN001', 'VIN002', 'VIN003', 'VIN004'],
            'start_date': ['2023-01-01', None, '2023-06-01', '2023-12-01'],
            'end_date': ['2023-12-31', '2023-12-31', None, '2023-01-01'],  # Invalid: end before start
            'is_currently_active': [False, False, True, False]
        }
        
        # Create temporary CSV file
        temp_csv = "example_results/problematic_timeline.csv"
        os.makedirs("example_results", exist_ok=True)
        
        df = pd.DataFrame(problematic_data)
        df.to_csv(temp_csv, index=False)
        
        print(f"📁 Created test data: {temp_csv}")
        print("📋 Test data contains intentional issues:")
        print("   - One good battery with complete dates")
        print("   - One battery with missing start date")
        print("   - One battery with missing end date (active)")
        print("   - One battery with invalid date range")
        
        # Process the problematic data
        output_csv = "example_results/battery_ages_errors.csv"
        
        calculator = BatteryAgeCalculator(
            timeline_csv_path=temp_csv,
            daily_stats_csv_path="daily_stats.csv"  # May not exist, that's OK
        )
        
        print("\n⏳ Processing problematic data...")
        results = calculator.calculate_battery_ages()
        
        # Export and analyze results
        output_file = calculator.export_results(output_csv)
        
        print(f"\n📊 Results analysis:")
        print(f"📁 Output file: {output_file}")
        
        # Categorize results
        successful = []
        errors = []
        
        for result in results:
            if result.age_days is not None:
                successful.append(result)
            else:
                errors.append(result)
        
        print(f"\n✅ Successful calculations: {len(successful)}")
        for result in successful:
            print(f"   • Battery {result.battery_id}: {result.age_days:.1f} days")
        
        print(f"\n❌ Error cases: {len(errors)}")
        for result in errors:
            print(f"   • Battery {result.battery_id}: {result.note}")
        
        print(f"\n💡 Interpretation guide:")
        print(f"   • Check the 'note' column in {output_file} for error details")
        print(f"   • Missing dates can often be resolved with better data")
        print(f"   • Invalid date ranges indicate data quality issues")
        print(f"   • Active batteries may need daily stats data for accurate end dates")
        
        # Clean up temporary file
        os.remove(temp_csv)
        
    except Exception as e:
        print(f"❌ Error in example: {e}")


def example_performance_considerations():
    """
    Example 4: Performance considerations for large datasets
    
    This example demonstrates best practices for processing large datasets
    and monitoring performance.
    """
    print("\n" + "="*60)
    print("EXAMPLE 4: Performance Considerations for Large Datasets")
    print("="*60)
    
    print("📈 Performance tips for large datasets:")
    print()
    
    print("1. 🗄️  Database Connection:")
    print("   • Use database connection for VIN mapping to avoid memory issues")
    print("   • Pre-load daily stats CSV for faster lookups")
    print("   • Consider connection pooling for very large datasets")
    print()
    
    print("2. 💾 Memory Management:")
    print("   • Monitor memory usage during processing")
    print("   • Consider processing in batches if memory is limited")
    print("   • Use appropriate data types (int32 vs int64, etc.)")
    print()
    
    print("3. ⚡ Processing Speed:")
    print("   • CSV pre-indexing provides faster daily stats lookups")
    print("   • Batch VIN mapping queries reduce database round trips")
    print("   • Progress reporting helps monitor long-running operations")
    print()
    
    print("4. 📊 Monitoring:")
    print("   • Enable verbose logging to track progress")
    print("   • Monitor success/error rates during processing")
    print("   • Check log files for detailed error information")
    print()
    
    # Demonstrate performance monitoring
    if os.path.exists("days_calculate/battery_timeline.csv"):
        print("🔍 Analyzing sample dataset performance characteristics:")
        
        try:
            # Load sample data to show size
            df = pd.read_csv("days_calculate/battery_timeline.csv")
            unique_batteries = df['battery_id'].nunique()
            unique_vins = df['vin'].nunique()
            total_records = len(df)
            
            print(f"   • Total timeline records: {total_records:,}")
            print(f"   • Unique batteries: {unique_batteries:,}")
            print(f"   • Unique VINs: {unique_vins:,}")
            print(f"   • Average records per battery: {total_records/unique_batteries:.1f}")
            
            # Estimate processing time
            estimated_seconds = max(1, total_records // 1000)  # Rough estimate
            print(f"   • Estimated processing time: ~{estimated_seconds} seconds")
            
            if total_records > 10000:
                print("   ⚠️  Large dataset detected - consider using verbose logging")
            
        except Exception as e:
            print(f"   ❌ Could not analyze sample data: {e}")
    
    print()
    print("💡 Command-line options for performance:")
    print("   python battery_age_calculator.py --verbose [files...]  # Detailed logging")
    print("   python battery_age_calculator.py --quiet [files...]   # Minimal output")
    print("   python battery_age_calculator.py --test-db           # Test DB connection")


def example_command_line_usage():
    """
    Example 5: Command-line usage patterns
    
    This example shows various ways to use the calculator from the command line.
    """
    print("\n" + "="*60)
    print("EXAMPLE 5: Command-Line Usage Patterns")
    print("="*60)
    
    print("🖥️  Basic command-line usage:")
    print()
    
    print("1. Basic usage with required files:")
    print("   python battery_age_calculator.py \\")
    print("     days_calculate/battery_timeline.csv \\")
    print("     daily_stats.csv \\")
    print("     output/battery_ages.csv")
    print()
    
    print("2. Using named arguments (recommended):")
    print("   python battery_age_calculator.py \\")
    print("     --timeline-csv days_calculate/battery_timeline.csv \\")
    print("     --daily-stats-csv daily_stats.csv \\")
    print("     --output-csv output/battery_ages.csv")
    print()
    
    print("3. With database connection string:")
    print("   python battery_age_calculator.py \\")
    print("     --timeline-csv timeline.csv \\")
    print("     --daily-stats-csv daily_stats.csv \\")
    print("     --output-csv results.csv \\")
    print("     --db-connection-string 'postgresql://user:pass@host:port/db'")
    print()
    
    print("4. Testing database connection:")
    print("   python battery_age_calculator.py --test-db")
    print()
    
    print("5. Verbose logging for debugging:")
    print("   python battery_age_calculator.py --verbose [other args...]")
    print()
    
    print("6. Quiet mode for automated scripts:")
    print("   python battery_age_calculator.py --quiet [other args...]")
    print()
    
    print("📋 Environment variables (alternative to command-line args):")
    print("   export DB_HOST=localhost")
    print("   export DB_PORT=6543")
    print("   export DB_NAME=LeitwartenDB")
    print("   export DB_USER=datadump")
    print("   export DB_PASSWORD=your_password")
    print()
    
    print("💡 Tips:")
    print("   • Use absolute paths for input files to avoid path issues")
    print("   • Create output directories before running the calculator")
    print("   • Test database connection first with --test-db")
    print("   • Use --verbose for troubleshooting data issues")


def main():
    """
    Main function that runs all examples
    """
    print("🔋 Battery Age Calculator - Usage Examples")
    print("=" * 60)
    print("This script demonstrates various usage patterns for the Battery Age Calculator.")
    print("Each example shows different aspects of the calculator's functionality.")
    
    try:
        # Run all examples
        # example_basic_usage()
        example_with_database()
        # example_error_handling()
        # example_performance_considerations()
        # example_command_line_usage()
        
        print("\n" + "="*60)
        print("🎉 All examples completed!")
        print("="*60)
        print()
        print("📚 Next steps:")
        print("   1. Review the generated example results in 'example_results/' directory")
        print("   2. Try running the calculator with your own data")
        print("   3. Use --verbose flag for detailed logging during processing")
        print("   4. Check the main script help: python battery_age_calculator.py --help")
        print()
        print("📖 For more information, see the docstrings in battery_age_calculator.py")
        
    except KeyboardInterrupt:
        print("\n⚠️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        logger.exception("Detailed error information:")


if __name__ == "__main__":
    main()